# Pre-commit configuration for Metamorphic Foundry
# Implements Development Workflow §2.3 requirements:
# - Black/Pyink for Python
# - gts for TypeScript  
# - YAML schema validation
# - Secret scans

repos:
  # Python formatting and linting
  - repo: https://github.com/psf/black
    rev: 24.10.0
    hooks:
      - id: black
        language_version: python3
        args: [--line-length=88]
        files: \.py$

  - repo: https://github.com/google/pyink
    rev: 24.10.0
    hooks:
      - id: pyink
        language_version: python3
        args: [--line-length=88, --pyink-indentation=2]
        files: \.py$

  # TypeScript formatting with Google TypeScript Style (gts)
  - repo: local
    hooks:
      - id: gts-check
        name: Google TypeScript Style Check
        entry: npx gts check
        language: node
        files: \.(ts|js)$
        pass_filenames: false
        additional_dependencies: [gts]

      - id: gts-fix
        name: Google TypeScript Style Fix
        entry: npx gts fix
        language: node
        files: \.(ts|js)$
        pass_filenames: false
        additional_dependencies: [gts]

  # YAML validation and formatting
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: check-yaml
        args: [--allow-multiple-documents]
      - id: check-json
      - id: pretty-format-json
        args: [--autofix, --indent=2]
      - id: end-of-file-fixer
      - id: trailing-whitespace
      - id: check-merge-conflict
      - id: check-added-large-files
        args: [--maxkb=1000]

  # Blueprint YAML schema validation
  - repo: local
    hooks:
      - id: blueprint-schema-validation
        name: Blueprint Schema Validation
        entry: python scripts/validate_blueprints.py
        language: python
        files: ^blueprints/.*\.yaml$
        additional_dependencies: [jsonschema, pyyaml]

  # Secret scanning
  - repo: https://github.com/Yelp/detect-secrets
    rev: v1.5.0
    hooks:
      - id: detect-secrets
        args: [--baseline, .secrets.baseline]
        exclude: |
          (?x)^(
            \.secrets\.baseline|
            \.git/.*|
            docs/.*\.md|
            .*\.lock|
            .*\.min\.(js|css)
          )$

  # Terraform validation (for infrastructure code)
  - repo: https://github.com/antonbabenko/pre-commit-terraform
    rev: v1.96.1
    hooks:
      - id: terraform_fmt
        files: \.tf$
      - id: terraform_validate
        files: \.tf$
      - id: terraform_docs
        args: [--hook-config=--path-to-file=README.md]
        files: \.tf$

  # Security and compliance checks
  - repo: https://github.com/PyCQA/bandit
    rev: 1.7.10
    hooks:
      - id: bandit
        args: [-r, -f, json, -o, bandit-report.json]
        files: \.py$

  # Dockerfile linting
  - repo: https://github.com/hadolint/hadolint
    rev: v2.12.0
    hooks:
      - id: hadolint-docker
        files: Dockerfile.*

  # Markdown linting
  - repo: https://github.com/igorshubovych/markdownlint-cli
    rev: v0.42.0
    hooks:
      - id: markdownlint
        args: [--fix]
        files: \.md$

  # Additional Python checks
  - repo: https://github.com/pycqa/isort
    rev: 5.13.2
    hooks:
      - id: isort
        args: [--profile, black, --line-length, "88"]
        files: \.py$

  - repo: https://github.com/pycqa/flake8
    rev: 7.1.1
    hooks:
      - id: flake8
        args: [--max-line-length=88, --extend-ignore=E203,W503]
        files: \.py$

# Global configuration
default_language_version:
  python: python3.11
  node: "18"

# CI configuration
ci:
  autofix_commit_msg: |
    [pre-commit.ci] auto fixes from pre-commit hooks

    for more information, see https://pre-commit.ci
  autofix_prs: true
  autoupdate_branch: ''
  autoupdate_commit_msg: '[pre-commit.ci] pre-commit autoupdate'
  autoupdate_schedule: weekly
  skip: []
  submodules: false
