# Metamorphic Foundry Agent Development Kit (ADK)

The Metamorphic Foundry ADK is a Python SDK and CLI tool for developing, testing, and deploying AI agents on the Metamorphic Foundry platform.

## Features

- **Blueprint Management**: Create, validate, and deploy agent blueprints
- **Testing Framework**: Built-in testing capabilities with observability
- **Observability Integration**: Automatic logging to BigQuery and Pub/Sub
- **Policy Validation**: Integration with OPA policies for compliance
- **CLI Tools**: Command-line interface for all operations
- **Metrics & Analytics**: Real-time performance monitoring

## Installation

```bash
pip install foundry-adk
```

Or install from source:

```bash
git clone https://github.com/your-org/metamorphic-foundry.git
cd metamorphic-foundry/adk
pip install -e .
```

## Quick Start

### 1. Set Environment Variables

```bash
export FOUNDRY_PROJECT_ID=your-gcp-project-id
export FOUNDRY_ENVIRONMENT=development
```

### 2. Initialize a New Project

```bash
foundry-adk init
```

### 3. Create an Agent Blueprint

```python
from foundry_adk import FoundryADK, create_agent

# Initialize ADK
adk = FoundryADK("your-project-id", "development")

# Create agent blueprint
blueprint = create_agent(
    name="hello-world",
    model="gemini-2.5-flash",
    instructions="You are a helpful AI assistant that greets users warmly.",
    tools=[
        {
            "type": "gcp.sheets.append",
            "auth": "service_account"
        }
    ]
)

# Save blueprint
blueprint.save("hello-world.yaml")
```

### 4. Validate and Deploy

```bash
# Validate blueprint
foundry-adk validate hello-world.yaml

# Deploy to development
foundry-adk deploy hello-world.yaml --environment development
```

## CLI Commands

### Create Blueprint

```bash
foundry-adk create \
  --name my-agent \
  --model gemini-2.5-flash \
  --instructions "You are a helpful assistant" \
  --output my-agent.yaml
```

### Validate Blueprint

```bash
foundry-adk validate my-agent.yaml
```

### Deploy Agent

```bash
foundry-adk deploy my-agent.yaml --environment production
```

### Test Agent

```bash
foundry-adk test my-agent.yaml --test-file test-cases.yaml
```

### Get Metrics

```bash
foundry-adk metrics my-agent --days 7
```

### List Deployed Agents

```bash
foundry-adk list --environment production
```

## Python API

### Basic Usage

```python
from foundry_adk import FoundryADK, AgentBlueprint

# Initialize
adk = FoundryADK("project-id", "development")

# Create blueprint
blueprint = adk.create_blueprint(
    name="customer-service",
    model="gemini-2.5-flash",
    instructions="You are a customer service agent...",
    tools=[
        {"type": "knowledge_base.search"},
        {"type": "ticket.create"}
    ]
)

# Validate
validation = adk.validate_blueprint(blueprint)
if validation['valid']:
    print("Blueprint is valid!")
else:
    print("Validation errors:", validation['violations'])

# Deploy
result = adk.deploy_blueprint(blueprint, "staging")
print(f"Deployed: {result['deployment_id']}")
```

### Testing

```python
# Define test cases
test_cases = [
    {
        "input": "I need help with my order",
        "expected_output": "I'd be happy to help with your order..."
    },
    {
        "input": "Cancel my subscription",
        "expected_output": "I can help you cancel your subscription..."
    }
]

# Run tests
results = adk.test_agent(blueprint, test_cases)
print(f"Tests passed: {results['summary']['passed']}/{results['summary']['total']}")
```

### Observability

```python
# Log agent turns for observability
request_id = adk.log_turn(
    agent_name="customer-service",
    agent_version="1.0.0",
    user_message="Hello",
    response="Hi! How can I help you?",
    processing_time_ms=250.5,
    model="gemini-2.5-flash",
    token_count=15,
    cost_usd=0.001
)

# Get performance metrics
metrics = adk.get_agent_metrics("customer-service", days=30)
print(f"Average latency: {metrics['metrics'][0]['avg_processing_time_ms']}ms")
```

## Blueprint Schema

Agent blueprints follow a structured schema:

```yaml
name: my-agent
version: 1.0.0
model: gemini-2.5-flash
instructions: |
  You are a helpful AI assistant...

tools:
  - type: gcp.sheets.append
    auth: service_account
  - type: calendar.send
    config:
      calendar_id: primary

memory:
  store: firestore
  retention_days: 30

security:
  model_armor_policy: PII_MED
  dlp_templates:
    - EMAIL_ADDRESS
    - PHONE_NUMBER

evaluation:
  metrics:
    - hallucination_rate <= 2%
    - toxicity_score <= 0.05
    - user_satisfaction >= 4.0

a2a:
  required_capabilities:
    - tool.call
    - message.send
  token_binding: true

metadata:
  owner: ai-team
  squad: customer-experience
  description: Customer service agent
  tags:
    - customer-service
    - production
```

## Configuration

### Environment Variables

- `FOUNDRY_PROJECT_ID`: Google Cloud Project ID
- `FOUNDRY_ENVIRONMENT`: Environment (development, staging, production)
- `GOOGLE_APPLICATION_CREDENTIALS`: Path to service account key (optional)

### Authentication

The ADK uses Google Cloud authentication. Ensure you have:

1. Google Cloud SDK installed and authenticated (`gcloud auth login`)
2. Or service account key file set in `GOOGLE_APPLICATION_CREDENTIALS`
3. Required IAM permissions:
   - BigQuery Data Editor
   - Pub/Sub Publisher
   - Firestore User

## Integration with Foundry Platform

The ADK integrates seamlessly with the Metamorphic Foundry platform:

- **Observability**: Automatic logging to BigQuery for analytics
- **Policy Validation**: OPA policy enforcement
- **Model Armor**: Content safety integration
- **A2A Protocol**: Agent-to-agent communication
- **Deployment Pipeline**: CI/CD integration with Cloud Build

## Examples

See the `examples/` directory for complete examples:

- `examples/hello-world/` - Simple greeting agent
- `examples/customer-service/` - Multi-tool customer service agent
- `examples/data-analyst/` - Agent with BigQuery integration
- `examples/multi-agent/` - A2A communication example

## Development

### Setup Development Environment

```bash
git clone https://github.com/your-org/metamorphic-foundry.git
cd metamorphic-foundry/adk
pip install -e ".[dev]"
pre-commit install
```

### Run Tests

```bash
pytest tests/
```

### Code Formatting

```bash
black .
flake8 .
mypy .
```

## Support

- Documentation: https://metamorphic-foundry.readthedocs.io/
- Issues: https://github.com/your-org/metamorphic-foundry/issues
- Discussions: https://github.com/your-org/metamorphic-foundry/discussions

## License

Apache License 2.0 - see LICENSE file for details.
