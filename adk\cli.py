#!/usr/bin/env python3
"""
Metamorphic Foundry ADK Command Line Interface
Provides CLI commands for agent development and management
"""

import click
import json
import yaml
import os
import sys
from pathlib import Path
from typing import Dict, Any, List

from foundry_adk import FoundryADK, AgentBlueprint, create_agent


@click.group()
@click.option('--project-id', envvar='FOUNDRY_PROJECT_ID', required=True,
              help='Google Cloud Project ID')
@click.option('--environment', envvar='FOUNDRY_ENVIRONMENT', default='development',
              help='Environment (development, staging, production)')
@click.option('--verbose', '-v', is_flag=True, help='Verbose output')
@click.pass_context
def cli(ctx, project_id: str, environment: str, verbose: bool):
    """Metamorphic Foundry Agent Development Kit CLI"""
    ctx.ensure_object(dict)
    ctx.obj['project_id'] = project_id
    ctx.obj['environment'] = environment
    ctx.obj['verbose'] = verbose
    
    if verbose:
        import logging
        logging.basicConfig(level=logging.DEBUG)
    
    # Initialize ADK
    try:
        ctx.obj['adk'] = FoundryADK(project_id, environment)
        if verbose:
            click.echo(f"Initialized ADK for project: {project_id}, environment: {environment}")
    except Exception as e:
        click.echo(f"Error initializing ADK: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.option('--name', required=True, help='Agent name')
@click.option('--model', required=True, 
              type=click.Choice(['gemini-2.5-pro', 'gemini-2.5-flash', 'gemini-2.5-flash-lite', 'gemma-3n', 'external']),
              help='Model to use')
@click.option('--instructions', required=True, help='Agent instructions')
@click.option('--output', '-o', help='Output file path')
@click.option('--format', type=click.Choice(['json', 'yaml']), default='yaml',
              help='Output format')
@click.pass_context
def create(ctx, name: str, model: str, instructions: str, output: str, format: str):
    """Create a new agent blueprint"""
    
    try:
        adk = ctx.obj['adk']
        
        # Create blueprint
        blueprint = adk.create_blueprint(
            name=name,
            model=model,
            instructions=instructions
        )
        
        if output:
            # Save to file
            output_path = Path(output)
            if not output_path.suffix:
                output_path = output_path.with_suffix(f'.{format}')
            
            blueprint.save(output_path)
            click.echo(f"Blueprint saved to: {output_path}")
        else:
            # Print to stdout
            if format == 'json':
                click.echo(json.dumps(blueprint.to_dict(), indent=2))
            else:
                click.echo(blueprint.to_yaml())
        
        if ctx.obj['verbose']:
            click.echo(f"Created blueprint: {name} using {model}")
            
    except Exception as e:
        click.echo(f"Error creating blueprint: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.argument('blueprint_file', type=click.Path(exists=True))
@click.pass_context
def validate(ctx, blueprint_file: str):
    """Validate an agent blueprint"""
    
    try:
        adk = ctx.obj['adk']
        
        # Load blueprint
        blueprint = AgentBlueprint.from_file(blueprint_file)
        
        # Validate
        result = adk.validate_blueprint(blueprint)
        
        if result['valid']:
            click.echo(click.style("✓ Blueprint is valid", fg='green'))
        else:
            click.echo(click.style("✗ Blueprint validation failed", fg='red'))
            for violation in result['violations']:
                click.echo(f"  - {violation}")
        
        if result['warnings']:
            click.echo(click.style("Warnings:", fg='yellow'))
            for warning in result['warnings']:
                click.echo(f"  - {warning}")
        
        if not result['valid']:
            sys.exit(1)
            
    except Exception as e:
        click.echo(f"Error validating blueprint: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.argument('blueprint_file', type=click.Path(exists=True))
@click.option('--environment', help='Target environment (overrides default)')
@click.option('--dry-run', is_flag=True, help='Validate only, do not deploy')
@click.pass_context
def deploy(ctx, blueprint_file: str, environment: str, dry_run: bool):
    """Deploy an agent blueprint"""
    
    try:
        adk = ctx.obj['adk']
        
        # Load blueprint
        blueprint = AgentBlueprint.from_file(blueprint_file)
        
        # Validate first
        validation = adk.validate_blueprint(blueprint)
        if not validation['valid']:
            click.echo(click.style("✗ Blueprint validation failed", fg='red'))
            for violation in validation['violations']:
                click.echo(f"  - {violation}")
            sys.exit(1)
        
        if dry_run:
            click.echo(click.style("✓ Blueprint is valid (dry-run mode)", fg='green'))
            return
        
        # Deploy
        target_env = environment or ctx.obj['environment']
        result = adk.deploy_blueprint(blueprint, target_env)
        
        click.echo(click.style(f"✓ Deployed {blueprint.name} v{blueprint.version} to {target_env}", fg='green'))
        click.echo(f"Deployment ID: {result['deployment_id']}")
        click.echo(f"Blueprint URL: {result['blueprint_url']}")
        
    except Exception as e:
        click.echo(f"Error deploying blueprint: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.argument('blueprint_file', type=click.Path(exists=True))
@click.option('--test-file', type=click.Path(exists=True), help='Test cases file (JSON/YAML)')
@click.option('--output', '-o', help='Test results output file')
@click.pass_context
def test(ctx, blueprint_file: str, test_file: str, output: str):
    """Test an agent blueprint"""
    
    try:
        adk = ctx.obj['adk']
        
        # Load blueprint
        blueprint = AgentBlueprint.from_file(blueprint_file)
        
        # Load test cases
        test_cases = []
        if test_file:
            with open(test_file, 'r') as f:
                if test_file.endswith('.json'):
                    test_data = json.load(f)
                else:
                    test_data = yaml.safe_load(f)
                
                test_cases = test_data.get('test_cases', test_data)
        else:
            # Default test cases
            test_cases = [
                {"input": "Hello", "expected_output": "Hello! How can I help you?"},
                {"input": "What is your name?", "expected_output": f"I am {blueprint.name}"}
            ]
        
        # Run tests
        click.echo(f"Running {len(test_cases)} test cases...")
        results = adk.test_agent(blueprint, test_cases)
        
        # Display results
        summary = results['summary']
        click.echo(f"\nTest Results:")
        click.echo(f"  Total: {summary['total']}")
        click.echo(click.style(f"  Passed: {summary['passed']}", fg='green'))
        if summary['failed'] > 0:
            click.echo(click.style(f"  Failed: {summary['failed']}", fg='red'))
        click.echo(f"  Avg Processing Time: {summary['avg_processing_time_ms']:.2f}ms")
        
        # Save results if requested
        if output:
            with open(output, 'w') as f:
                json.dump(results, f, indent=2)
            click.echo(f"\nResults saved to: {output}")
        
        if summary['failed'] > 0:
            sys.exit(1)
            
    except Exception as e:
        click.echo(f"Error testing blueprint: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.argument('agent_name')
@click.option('--days', default=7, help='Number of days to look back')
@click.option('--format', type=click.Choice(['json', 'table']), default='table',
              help='Output format')
@click.pass_context
def metrics(ctx, agent_name: str, days: int, format: str):
    """Get agent performance metrics"""
    
    try:
        adk = ctx.obj['adk']
        
        # Get metrics
        result = adk.get_agent_metrics(agent_name, days)
        
        if 'error' in result:
            click.echo(f"Error getting metrics: {result['error']}", err=True)
            sys.exit(1)
        
        if format == 'json':
            click.echo(json.dumps(result, indent=2))
        else:
            # Table format
            metrics = result['metrics']
            if not metrics:
                click.echo(f"No metrics found for agent: {agent_name}")
                return
            
            click.echo(f"\nMetrics for {agent_name} (last {days} days):")
            click.echo("-" * 60)
            
            for metric in metrics:
                click.echo(f"Version: {metric['agent_version']}")
                click.echo(f"  Total Turns: {metric['total_turns']}")
                click.echo(f"  Avg Processing Time: {metric['avg_processing_time_ms']:.2f}ms")
                click.echo(f"  P95 Processing Time: {metric['p95_processing_time_ms']:.2f}ms")
                if metric['avg_hallucination_rate'] is not None:
                    click.echo(f"  Avg Hallucination Rate: {metric['avg_hallucination_rate']:.4f}")
                if metric['avg_toxicity_score'] is not None:
                    click.echo(f"  Avg Toxicity Score: {metric['avg_toxicity_score']:.4f}")
                click.echo(f"  Error Count: {metric['error_count']}")
                click.echo(f"  Total Tokens: {metric['total_tokens']}")
                if metric['total_cost_usd'] is not None:
                    click.echo(f"  Total Cost: ${metric['total_cost_usd']:.4f}")
                click.echo()
        
    except Exception as e:
        click.echo(f"Error getting metrics: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.option('--name', help='Filter by agent name')
@click.option('--environment', help='Filter by environment')
@click.option('--limit', default=10, help='Number of results to show')
@click.pass_context
def list(ctx, name: str, environment: str, limit: int):
    """List deployed agents"""
    
    try:
        adk = ctx.obj['adk']
        
        # Query Firestore for deployed agents
        collection_ref = adk.firestore_client.collection("agent_blueprints")
        query = collection_ref.limit(limit)
        
        if name:
            query = query.where("name", "==", name)
        if environment:
            query = query.where("environment", "==", environment)
        
        docs = query.stream()
        
        agents = []
        for doc in docs:
            data = doc.to_dict()
            agents.append({
                "name": data.get("name"),
                "version": data.get("version"),
                "model": data.get("model"),
                "environment": data.get("environment"),
                "deployed_at": data.get("deployed_at"),
                "deployment_id": data.get("deployment_id")
            })
        
        if not agents:
            click.echo("No deployed agents found")
            return
        
        click.echo(f"\nDeployed Agents:")
        click.echo("-" * 80)
        click.echo(f"{'Name':<20} {'Version':<10} {'Model':<20} {'Environment':<15} {'Deployed'}")
        click.echo("-" * 80)
        
        for agent in agents:
            deployed_at = agent['deployed_at']
            if deployed_at:
                deployed_str = deployed_at.strftime("%Y-%m-%d %H:%M") if hasattr(deployed_at, 'strftime') else str(deployed_at)[:16]
            else:
                deployed_str = "Unknown"
            
            click.echo(f"{agent['name']:<20} {agent['version']:<10} {agent['model']:<20} {agent['environment']:<15} {deployed_str}")
        
    except Exception as e:
        click.echo(f"Error listing agents: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.pass_context
def init(ctx):
    """Initialize a new agent project"""
    
    try:
        # Create project structure
        project_name = click.prompt("Project name", type=str)
        
        # Create directories
        project_dir = Path(project_name)
        project_dir.mkdir(exist_ok=True)
        
        (project_dir / "blueprints").mkdir(exist_ok=True)
        (project_dir / "tests").mkdir(exist_ok=True)
        (project_dir / "docs").mkdir(exist_ok=True)
        
        # Create sample blueprint
        sample_blueprint = {
            "name": project_name,
            "version": "0.1.0",
            "model": "gemini-2.5-flash",
            "instructions": f"You are {project_name}, a helpful AI assistant.",
            "tools": [],
            "security": {
                "model_armor_policy": "PII_LOW"
            },
            "evaluation": {
                "metrics": [
                    "hallucination_rate <= 2%",
                    "toxicity_score <= 0.05"
                ]
            },
            "metadata": {
                "owner": "development-team",
                "squad": "ai-agents",
                "description": f"{project_name} agent blueprint",
                "tags": ["sample", "development"]
            }
        }
        
        with open(project_dir / "blueprints" / f"{project_name}.yaml", 'w') as f:
            yaml.dump(sample_blueprint, f, default_flow_style=False)
        
        # Create sample test file
        sample_tests = {
            "test_cases": [
                {
                    "input": "Hello",
                    "expected_output": "Hello! How can I help you?"
                },
                {
                    "input": "What is your name?",
                    "expected_output": f"I am {project_name}"
                }
            ]
        }
        
        with open(project_dir / "tests" / "test_cases.yaml", 'w') as f:
            yaml.dump(sample_tests, f, default_flow_style=False)
        
        # Create README
        readme_content = f"""# {project_name}

Agent project created with Metamorphic Foundry ADK.

## Structure

- `blueprints/` - Agent blueprint definitions
- `tests/` - Test cases and test results
- `docs/` - Documentation

## Usage

```bash
# Validate blueprint
foundry-adk validate blueprints/{project_name}.yaml

# Test agent
foundry-adk test blueprints/{project_name}.yaml --test-file tests/test_cases.yaml

# Deploy agent
foundry-adk deploy blueprints/{project_name}.yaml --environment development
```

## Environment Variables

Set these environment variables:

```bash
export FOUNDRY_PROJECT_ID=your-gcp-project-id
export FOUNDRY_ENVIRONMENT=development
```
"""
        
        with open(project_dir / "README.md", 'w') as f:
            f.write(readme_content)
        
        click.echo(click.style(f"✓ Initialized project: {project_name}", fg='green'))
        click.echo(f"Project created in: {project_dir.absolute()}")
        click.echo("\nNext steps:")
        click.echo(f"  cd {project_name}")
        click.echo(f"  foundry-adk validate blueprints/{project_name}.yaml")
        
    except Exception as e:
        click.echo(f"Error initializing project: {e}", err=True)
        sys.exit(1)


if __name__ == '__main__':
    cli()
