#!/usr/bin/env python3
"""
Hello World Example for Metamorphic Foundry ADK
Demonstrates basic agent creation, testing, and deployment
"""

import os
import json
from foundry_adk import FoundryADK, create_agent

def main():
    # Initialize ADK
    project_id = os.environ.get('FOUNDRY_PROJECT_ID', 'your-project-id')
    environment = os.environ.get('FOUNDRY_ENVIRONMENT', 'development')
    
    print(f"Initializing Foundry ADK for project: {project_id}")
    adk = FoundryADK(project_id, environment)
    
    # Create a simple hello world agent
    print("\n1. Creating Hello World agent blueprint...")
    blueprint = create_agent(
        name="hello-world",
        model="gemini-2.5-flash",
        instructions="""
        You are a friendly AI assistant named Hello World.
        Your job is to greet users warmly and provide helpful responses.
        Always be polite, concise, and helpful.
        """,
        tools=[
            {
                "type": "gcp.sheets.append",
                "auth": "service_account",
                "config": {
                    "spreadsheet_id": "example-sheet-id"
                }
            }
        ],
        memory={
            "store": "firestore",
            "retention_days": 30
        },
        security={
            "model_armor_policy": "PII_LOW",
            "dlp_templates": ["EMAIL_ADDRESS"]
        },
        evaluation={
            "metrics": [
                "hallucination_rate <= 2%",
                "toxicity_score <= 0.05",
                "user_satisfaction >= 4.0"
            ]
        },
        metadata={
            "owner": "adk-examples",
            "squad": "development",
            "description": "Hello World demonstration agent",
            "tags": ["example", "hello-world", "demo"]
        }
    )
    
    print(f"✓ Created blueprint: {blueprint.name} v{blueprint.version}")
    
    # Validate the blueprint
    print("\n2. Validating blueprint...")
    validation = adk.validate_blueprint(blueprint)
    
    if validation['valid']:
        print("✓ Blueprint validation passed")
    else:
        print("✗ Blueprint validation failed:")
        for violation in validation['violations']:
            print(f"  - {violation}")
        return
    
    if validation['warnings']:
        print("⚠ Warnings:")
        for warning in validation['warnings']:
            print(f"  - {warning}")
    
    # Save blueprint to file
    print("\n3. Saving blueprint...")
    blueprint.save("hello-world.yaml")
    print("✓ Blueprint saved to hello-world.yaml")
    
    # Define test cases
    print("\n4. Running tests...")
    test_cases = [
        {
            "input": "Hello",
            "expected_output": "Hello! How can I help you today?"
        },
        {
            "input": "What is your name?",
            "expected_output": "I am Hello World, your friendly AI assistant."
        },
        {
            "input": "How are you?",
            "expected_output": "I'm doing great, thank you for asking!"
        },
        {
            "input": "Can you help me with something?",
            "expected_output": "Of course! I'd be happy to help you."
        },
        {
            "input": "Goodbye",
            "expected_output": "Goodbye! Have a wonderful day!"
        }
    ]
    
    # Run tests
    test_results = adk.test_agent(blueprint, test_cases)
    
    # Display test results
    summary = test_results['summary']
    print(f"✓ Tests completed:")
    print(f"  Total: {summary['total']}")
    print(f"  Passed: {summary['passed']}")
    print(f"  Failed: {summary['failed']}")
    print(f"  Avg Processing Time: {summary['avg_processing_time_ms']:.2f}ms")
    
    # Save test results
    with open("hello-world-test-results.json", 'w') as f:
        json.dump(test_results, f, indent=2)
    print("✓ Test results saved to hello-world-test-results.json")
    
    # Deploy to development environment
    print(f"\n5. Deploying to {environment} environment...")
    try:
        deployment_result = adk.deploy_blueprint(blueprint, environment)
        print(f"✓ Deployment successful!")
        print(f"  Deployment ID: {deployment_result['deployment_id']}")
        print(f"  Environment: {deployment_result['environment']}")
        print(f"  Blueprint URL: {deployment_result['blueprint_url']}")
    except Exception as e:
        print(f"✗ Deployment failed: {e}")
        return
    
    # Simulate some agent usage for metrics
    print("\n6. Simulating agent usage...")
    for i, test_case in enumerate(test_cases[:3]):  # Use first 3 test cases
        request_id = adk.log_turn(
            agent_name=blueprint.name,
            agent_version=blueprint.version,
            user_message=test_case["input"],
            response=f"Simulated response for: {test_case['input']}",
            processing_time_ms=150.0 + (i * 50),  # Simulate varying latency
            model=blueprint.model,
            token_count=20 + (i * 5),
            cost_usd=0.001 + (i * 0.0005),
            hallucination_rate=0.01,  # Well within SLA
            toxicity_score=0.02,      # Well within SLA
            user_satisfaction=4.5,
            model_armor_verdict="ALLOW",
            tools_used=["gcp.sheets.append"] if i == 0 else [],
            metadata=json.dumps({"example_run": True, "test_case": i + 1})
        )
        print(f"  Logged turn {i + 1}: {request_id}")
    
    print("\n7. Getting agent metrics...")
    try:
        # Note: In a real scenario, you'd wait for data to be processed
        # For this example, we'll just show how to call the metrics function
        metrics = adk.get_agent_metrics(blueprint.name, days=1)
        
        if metrics['metrics']:
            print("✓ Metrics retrieved:")
            for metric in metrics['metrics']:
                print(f"  Version: {metric['agent_version']}")
                print(f"  Total Turns: {metric['total_turns']}")
                print(f"  Avg Processing Time: {metric['avg_processing_time_ms']:.2f}ms")
        else:
            print("ℹ No metrics available yet (data may still be processing)")
    except Exception as e:
        print(f"ℹ Metrics not available: {e}")
    
    print("\n🎉 Hello World example completed successfully!")
    print("\nNext steps:")
    print("1. Check the Looker dashboards for observability data")
    print("2. Modify the blueprint and redeploy")
    print("3. Explore more advanced features like A2A communication")
    print("4. Set up CI/CD pipeline for automated deployments")


if __name__ == "__main__":
    main()
