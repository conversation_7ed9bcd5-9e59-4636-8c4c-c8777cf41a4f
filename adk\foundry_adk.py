"""
Metamorphic Foundry Agent Development Kit (ADK)
Provides Python SDK for agent development, testing, and deployment
"""

import json
import os
import time
import uuid
from dataclasses import dataclass, asdict
from typing import Dict, List, Optional, Any, Union, Callable
from pathlib import Path
import hashlib
import logging
from datetime import datetime, timezone

import requests
import yaml
from google.cloud import bigquery, pubsub_v1, firestore
from google.auth import default


# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class AgentBlueprint:
    """Agent blueprint data structure matching the JSONSchema"""
    name: str
    version: str
    model: str
    instructions: str
    tools: List[Dict[str, Any]]
    memory: Optional[Dict[str, Any]] = None
    evaluation: Optional[Dict[str, Any]] = None
    security: Optional[Dict[str, Any]] = None
    a2a: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert blueprint to dictionary"""
        return asdict(self)
    
    def to_yaml(self) -> str:
        """Convert blueprint to YAML string"""
        return yaml.dump(self.to_dict(), default_flow_style=False)
    
    def save(self, filepath: Union[str, Path]) -> None:
        """Save blueprint to file"""
        filepath = Path(filepath)
        if filepath.suffix == '.json':
            with open(filepath, 'w') as f:
                json.dump(self.to_dict(), f, indent=2)
        elif filepath.suffix in ['.yaml', '.yml']:
            with open(filepath, 'w') as f:
                f.write(self.to_yaml())
        else:
            raise ValueError("File must have .json, .yaml, or .yml extension")
    
    @classmethod
    def from_file(cls, filepath: Union[str, Path]) -> 'AgentBlueprint':
        """Load blueprint from file"""
        filepath = Path(filepath)
        with open(filepath, 'r') as f:
            if filepath.suffix == '.json':
                data = json.load(f)
            elif filepath.suffix in ['.yaml', '.yml']:
                data = yaml.safe_load(f)
            else:
                raise ValueError("File must have .json, .yaml, or .yml extension")
        
        return cls(**data)


@dataclass
class AgentTurn:
    """Agent turn data for observability"""
    agent_id: str
    request_id: str
    agent_name: str
    agent_version: str
    model: str
    environment: str
    timestamp: str
    user_message_hash: Optional[str] = None
    response_hash: Optional[str] = None
    processing_time_ms: Optional[float] = None
    token_count: Optional[int] = None
    cost_usd: Optional[float] = None
    hallucination_rate: Optional[float] = None
    toxicity_score: Optional[float] = None
    user_satisfaction: Optional[float] = None
    model_armor_verdict: Optional[str] = None
    tools_used: Optional[List[str]] = None
    error_message: Optional[str] = None
    metadata: Optional[str] = None


class FoundryADK:
    """Main ADK class for Metamorphic Foundry integration"""
    
    def __init__(self, project_id: str, environment: str = "development"):
        self.project_id = project_id
        self.environment = environment
        self.agent_id = str(uuid.uuid4())
        
        # Initialize Google Cloud clients
        self.credentials, _ = default()
        self.bigquery_client = bigquery.Client(project=project_id, credentials=self.credentials)
        self.publisher = pubsub_v1.PublisherClient(credentials=self.credentials)
        self.firestore_client = firestore.Client(project=project_id, credentials=self.credentials)
        
        # Pub/Sub topic paths
        self.turns_topic = f"projects/{project_id}/topics/agent-turns"
        self.deployments_topic = f"projects/{project_id}/topics/agent-deployments"
        self.violations_topic = f"projects/{project_id}/topics/policy-violations"
        
        logger.info(f"FoundryADK initialized for project: {project_id}, environment: {environment}")
    
    def create_blueprint(self, 
                        name: str,
                        model: str,
                        instructions: str,
                        tools: List[Dict[str, Any]] = None,
                        **kwargs) -> AgentBlueprint:
        """Create a new agent blueprint"""
        
        blueprint = AgentBlueprint(
            name=name,
            version="0.1.0",
            model=model,
            instructions=instructions,
            tools=tools or [],
            **kwargs
        )
        
        # Set default metadata if not provided
        if not blueprint.metadata:
            blueprint.metadata = {
                "owner": "adk-user",
                "squad": "development",
                "description": f"Agent blueprint created via ADK",
                "tags": ["adk", "development"]
            }
        
        # Set default security if not provided
        if not blueprint.security:
            blueprint.security = {
                "model_armor_policy": "PII_LOW"
            }
        
        # Set default evaluation if not provided
        if not blueprint.evaluation:
            blueprint.evaluation = {
                "metrics": [
                    "hallucination_rate <= 2%",
                    "toxicity_score <= 0.05"
                ]
            }
        
        logger.info(f"Created blueprint: {name}")
        return blueprint
    
    def validate_blueprint(self, blueprint: AgentBlueprint) -> Dict[str, Any]:
        """Validate blueprint against OPA policies"""
        
        # This would integrate with the OPA policy engine
        # For now, we'll do basic validation
        
        validation_result = {
            "valid": True,
            "violations": [],
            "warnings": []
        }
        
        # Basic validations
        if not blueprint.name.replace('-', '').replace('_', '').isalnum():
            validation_result["violations"].append("Name must be alphanumeric with hyphens/underscores")
            validation_result["valid"] = False
        
        if blueprint.model not in ["gemini-2.5-pro", "gemini-2.5-flash", "gemini-2.5-flash-lite", "gemma-3n", "external"]:
            validation_result["violations"].append(f"Unsupported model: {blueprint.model}")
            validation_result["valid"] = False
        
        if len(blueprint.instructions.split()) > 8192:  # Simplified token counting
            validation_result["violations"].append("Instructions exceed token limit")
            validation_result["valid"] = False
        
        # Check for secrets in instructions
        secret_patterns = ["password", "api_key", "secret", "token"]
        for pattern in secret_patterns:
            if pattern in blueprint.instructions.lower():
                validation_result["warnings"].append(f"Potential secret detected: {pattern}")
        
        logger.info(f"Blueprint validation: {'PASSED' if validation_result['valid'] else 'FAILED'}")
        return validation_result
    
    def deploy_blueprint(self, blueprint: AgentBlueprint, target_environment: str = None) -> Dict[str, Any]:
        """Deploy blueprint to specified environment"""
        
        target_env = target_environment or self.environment
        
        # Validate blueprint first
        validation = self.validate_blueprint(blueprint)
        if not validation["valid"]:
            raise ValueError(f"Blueprint validation failed: {validation['violations']}")
        
        deployment_id = str(uuid.uuid4())
        
        # Create deployment record
        deployment_data = {
            "deployment_id": deployment_id,
            "agent_name": blueprint.name,
            "agent_version": blueprint.version,
            "environment": target_env,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "git_commit": os.environ.get("GIT_COMMIT", "unknown"),
            "git_branch": os.environ.get("GIT_BRANCH", "unknown"),
            "deployment_status": "success",
            "a2a_manifest": json.dumps(blueprint.a2a) if blueprint.a2a else None,
            "canary_pass_rate": 100.0,  # Simplified for ADK
            "deployed_by": os.environ.get("USER", "adk-user")
        }
        
        # Publish deployment event
        self._publish_deployment_event(deployment_data)
        
        # Store blueprint in Firestore
        doc_ref = self.firestore_client.collection("agent_blueprints").document(f"{blueprint.name}-{blueprint.version}")
        doc_ref.set({
            **blueprint.to_dict(),
            "deployed_at": datetime.now(timezone.utc),
            "deployment_id": deployment_id,
            "environment": target_env
        })
        
        logger.info(f"Deployed blueprint {blueprint.name} v{blueprint.version} to {target_env}")
        
        return {
            "deployment_id": deployment_id,
            "status": "success",
            "environment": target_env,
            "blueprint_url": f"https://console.cloud.google.com/firestore/data/agent_blueprints/{blueprint.name}-{blueprint.version}?project={self.project_id}"
        }
    
    def log_turn(self, 
                agent_name: str,
                agent_version: str,
                user_message: str = None,
                response: str = None,
                processing_time_ms: float = None,
                **kwargs) -> str:
        """Log an agent turn for observability"""
        
        request_id = str(uuid.uuid4())
        
        turn_data = AgentTurn(
            agent_id=self.agent_id,
            request_id=request_id,
            agent_name=agent_name,
            agent_version=agent_version,
            model=kwargs.get("model", "unknown"),
            environment=self.environment,
            timestamp=datetime.now(timezone.utc).isoformat(),
            user_message_hash=self._hash_content(user_message) if user_message else None,
            response_hash=self._hash_content(response) if response else None,
            processing_time_ms=processing_time_ms,
            **{k: v for k, v in kwargs.items() if k not in ["model"]}
        )
        
        # Publish turn event
        self._publish_turn_event(asdict(turn_data))
        
        logger.debug(f"Logged turn: {request_id}")
        return request_id
    
    def test_agent(self, 
                  blueprint: AgentBlueprint,
                  test_cases: List[Dict[str, Any]],
                  **kwargs) -> Dict[str, Any]:
        """Test agent with provided test cases"""
        
        test_results = {
            "blueprint": blueprint.name,
            "version": blueprint.version,
            "test_run_id": str(uuid.uuid4()),
            "started_at": datetime.now(timezone.utc).isoformat(),
            "test_cases": [],
            "summary": {
                "total": len(test_cases),
                "passed": 0,
                "failed": 0,
                "avg_processing_time_ms": 0
            }
        }
        
        total_processing_time = 0
        
        for i, test_case in enumerate(test_cases):
            start_time = time.time()
            
            # Simulate agent processing (in real implementation, this would call the actual agent)
            test_result = {
                "test_case_id": i + 1,
                "input": test_case.get("input", ""),
                "expected_output": test_case.get("expected_output"),
                "actual_output": f"Simulated response for: {test_case.get('input', '')}",
                "processing_time_ms": 0,
                "passed": True,  # Simplified for ADK
                "assertions": []
            }
            
            processing_time = (time.time() - start_time) * 1000
            test_result["processing_time_ms"] = processing_time
            total_processing_time += processing_time
            
            # Log the test turn
            self.log_turn(
                agent_name=blueprint.name,
                agent_version=blueprint.version,
                user_message=test_case.get("input"),
                response=test_result["actual_output"],
                processing_time_ms=processing_time,
                model=blueprint.model,
                metadata=json.dumps({"test_run": True, "test_case_id": i + 1})
            )
            
            if test_result["passed"]:
                test_results["summary"]["passed"] += 1
            else:
                test_results["summary"]["failed"] += 1
            
            test_results["test_cases"].append(test_result)
        
        test_results["summary"]["avg_processing_time_ms"] = total_processing_time / len(test_cases) if test_cases else 0
        test_results["completed_at"] = datetime.now(timezone.utc).isoformat()
        
        logger.info(f"Test completed: {test_results['summary']['passed']}/{test_results['summary']['total']} passed")
        return test_results
    
    def get_agent_metrics(self, agent_name: str, days: int = 7) -> Dict[str, Any]:
        """Get agent performance metrics from BigQuery"""
        
        query = f"""
        SELECT
            agent_name,
            agent_version,
            COUNT(*) as total_turns,
            AVG(processing_time_ms) as avg_processing_time_ms,
            APPROX_QUANTILES(processing_time_ms, 100)[OFFSET(95)] as p95_processing_time_ms,
            AVG(CASE WHEN hallucination_rate IS NOT NULL THEN hallucination_rate END) as avg_hallucination_rate,
            AVG(CASE WHEN toxicity_score IS NOT NULL THEN toxicity_score END) as avg_toxicity_score,
            SUM(CASE WHEN error_message IS NOT NULL THEN 1 ELSE 0 END) as error_count,
            SUM(token_count) as total_tokens,
            SUM(cost_usd) as total_cost_usd
        FROM `{self.project_id}.foundry_observability.agent_turns`
        WHERE agent_name = @agent_name
            AND timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL @days DAY)
        GROUP BY agent_name, agent_version
        ORDER BY agent_version DESC
        """
        
        job_config = bigquery.QueryJobConfig(
            query_parameters=[
                bigquery.ScalarQueryParameter("agent_name", "STRING", agent_name),
                bigquery.ScalarQueryParameter("days", "INT64", days),
            ]
        )
        
        try:
            query_job = self.bigquery_client.query(query, job_config=job_config)
            results = query_job.result()
            
            metrics = []
            for row in results:
                metrics.append(dict(row))
            
            return {
                "agent_name": agent_name,
                "metrics": metrics,
                "query_timestamp": datetime.now(timezone.utc).isoformat()
            }
        except Exception as e:
            logger.error(f"Failed to get metrics for {agent_name}: {e}")
            return {"agent_name": agent_name, "metrics": [], "error": str(e)}
    
    def _hash_content(self, content: str) -> str:
        """Hash content for privacy"""
        return hashlib.sha256(content.encode()).hexdigest()[:16]
    
    def _publish_turn_event(self, turn_data: Dict[str, Any]) -> None:
        """Publish turn event to Pub/Sub"""
        try:
            message_data = json.dumps(turn_data).encode('utf-8')
            future = self.publisher.publish(self.turns_topic, message_data)
            future.result()  # Wait for publish to complete
        except Exception as e:
            logger.error(f"Failed to publish turn event: {e}")
    
    def _publish_deployment_event(self, deployment_data: Dict[str, Any]) -> None:
        """Publish deployment event to Pub/Sub"""
        try:
            message_data = json.dumps(deployment_data).encode('utf-8')
            future = self.publisher.publish(self.deployments_topic, message_data)
            future.result()  # Wait for publish to complete
        except Exception as e:
            logger.error(f"Failed to publish deployment event: {e}")


# Convenience functions for quick usage
def create_agent(name: str, model: str, instructions: str, **kwargs) -> AgentBlueprint:
    """Quick agent creation function"""
    return AgentBlueprint(
        name=name,
        version="0.1.0",
        model=model,
        instructions=instructions,
        tools=kwargs.get("tools", []),
        **{k: v for k, v in kwargs.items() if k != "tools"}
    )


def init_foundry(project_id: str, environment: str = "development") -> FoundryADK:
    """Initialize Foundry ADK"""
    return FoundryADK(project_id, environment)
