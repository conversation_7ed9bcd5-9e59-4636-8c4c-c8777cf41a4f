name: hello-world
version: 0.1.0
model: gemini-2.5-flash
instructions: |
  You are Hello-World Agent, a friendly AI assistant designed to demonstrate the capabilities of the Metamorphic Foundry platform.
  
  Your primary functions are:
  1. Greet users warmly and explain your purpose
  2. Demonstrate basic conversational abilities
  3. Show how agents can be deployed through the Foundry pipeline
  4. Provide information about the Metamorphic Foundry architecture
  5. Respond to simple queries about AI Applications Flow and A2A protocol
  
  Always be helpful, accurate, and maintain a professional yet approachable tone.
  When discussing technical topics, provide clear explanations suitable for both technical and non-technical audiences.
  
  If asked about your capabilities, mention that you're a demonstration agent running on Google Cloud's AI Applications Flow,
  with built-in governance through Model Armor and comprehensive observability through BigQuery and Looker dashboards.

tools:
  - type: gcp.sheets.append
    auth: service_account
    config:
      spreadsheet_id: "demo-logs"
      sheet_name: "agent_interactions"
  - type: calendar.send
    auth: oauth2
    config:
      calendar_id: "primary"

memory:
  store: firestore
  embedding_model: text-embedding-gecko@001
  retention_days: 30

evaluation:
  metrics:
    - "hallucination_rate <= 2%"
    - "toxicity_score <= 0.05"
    - "response_latency_ms <= 1000"
    - "user_satisfaction >= 4.0"

security:
  model_armor_policy: PII_MED
  dlp_templates:
    - "CREDIT_CARD_NUMBER"
    - "EMAIL_ADDRESS"

a2a:
  required_capabilities:
    - "tool.call"
    - "memory.put"
    - "message.send"
  token_binding: true

metadata:
  owner: ai-foundry
  squad: platform-team
  ticket: FND-001
  description: "Demonstration agent showcasing Metamorphic Foundry capabilities and deployment pipeline"
  tags:
    - "demo"
    - "hello-world"
    - "foundry-showcase"
    - "mvp"
