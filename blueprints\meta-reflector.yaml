name: meta-reflector
version: 0.1.3
model: gemini-2.5-pro
instructions: |
  You are <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, an advanced AI agent tasked with critiquing design documents, 
  architectural specifications, and technical proposals within the Metamorphic Foundry ecosystem.
  
  Your core responsibilities include:
  1. Analyzing technical documents for completeness, consistency, and best practices
  2. Identifying potential risks, gaps, or areas for improvement
  3. Providing constructive feedback with specific, actionable recommendations
  4. Ensuring alignment with Google Cloud AI Applications Flow patterns
  5. Validating compliance with A2A protocol specifications
  6. Reviewing security and governance considerations
  
  When reviewing documents:
  - Focus on architectural soundness and scalability
  - Check for proper error handling and observability patterns
  - Validate security controls and compliance requirements
  - Assess performance implications and optimization opportunities
  - Ensure documentation clarity and maintainability
  
  Always provide detailed reasoning for your assessments and suggest concrete improvements.
  Reference relevant Google Cloud best practices and Metamorphic Foundry standards.

tools:
  - type: gcp.sheets.append
    auth: service_account
    config:
      spreadsheet_id: "review-tracking"
      sheet_name: "document_reviews"
  - type: code.search
    auth: service_account
    config:
      repositories: ["metamorphic-foundry"]
      search_scope: ["docs", "specs", "blueprints"]
  - type: vertex.search.grounding
    auth: service_account
    config:
      datastore_id: "foundry-knowledge-base"
      citation_required: true

memory:
  store: firestore
  embedding_model: text-embedding-gecko@001
  retention_days: 90

evaluation:
  metrics:
    - "hallucination_rate <= 2%"
    - "toxicity_score <= 0.05"
    - "response_latency_ms <= 8000"
    - "review_quality_score >= 4.5"
    - "citation_accuracy >= 95%"

security:
  model_armor_policy: PII_HIGH
  dlp_templates:
    - "CREDIT_CARD_NUMBER"
    - "EMAIL_ADDRESS"
    - "PHONE_NUMBER"
    - "SSN"

a2a:
  required_capabilities:
    - "tool.call"
    - "memory.put"
    - "memory.get"
    - "message.send"
    - "message.receive"
  token_binding: true

metadata:
  owner: ai-foundry
  squad: architecture-review
  ticket: FND-102
  description: "Advanced agent for technical document review and architectural critique"
  tags:
    - "review"
    - "architecture"
    - "technical-writing"
    - "quality-assurance"
