# Metamorphic Foundry CI/CD Pipeline
# Implements Development Workflow §3: 3-stage pipeline (lint → security → canary)
# Supports blueprint validation, OCI image builds, and blue-green deployments

substitutions:
  _REGION: us-central1
  _ARTIFACT_REGISTRY: ${_REGION}-docker.pkg.dev/${PROJECT_ID}/ai-agents
  _STAGING_CLUSTER: foundry-staging
  _PROD_CLUSTER: foundry-prod

options:
  logging: CLOUD_LOGGING_ONLY
  pool:
    name: projects/${PROJECT_ID}/locations/${_REGION}/workerPools/foundry-build-pool
  machineType: E2_HIGHCPU_8
  substitution_option: ALLOW_LOOSE

# Stage 0: Lint & Unit Tests
steps:
  # Pre-commit hooks validation
  - name: python:3.11-slim
    id: pre-commit-validation
    entrypoint: bash
    args:
      - -c
      - |
        pip install pre-commit
        pre-commit run --all-files
    waitFor: ['-']

  # Python linting and testing
  - name: python:3.11-slim
    id: python-lint-test
    entrypoint: bash
    args:
      - -c
      - |
        pip install pyright pytest google-python-style-guide
        # Run pyright type checking
        pyright scripts/ || exit 1
        # Run pytest unit tests
        pytest tests/ --verbose || exit 1
    waitFor: ['pre-commit-validation']

  # TypeScript linting with gts
  - name: node:18-slim
    id: typescript-lint
    entrypoint: bash
    args:
      - -c
      - |
        npm install -g gts
        # Check for TypeScript files and run gts
        if find . -name "*.ts" -o -name "*.js" | grep -q .; then
          gts check || exit 1
        else
          echo "No TypeScript files found, skipping gts check"
        fi
    waitFor: ['pre-commit-validation']

  # Blueprint schema validation
  - name: python:3.11-slim
    id: blueprint-validation
    entrypoint: bash
    args:
      - -c
      - |
        pip install jsonschema pyyaml
        python scripts/validate_blueprints.py blueprints/*.yaml
    waitFor: ['pre-commit-validation']

# Stage 1: Security Gates
  # Model Armor synthetic prompt scanning
  - name: gcr.io/google.com/cloudsdktool/cloud-sdk:latest
    id: model-armor-scan
    entrypoint: bash
    args:
      - -c
      - |
        # Extract instructions from blueprints and test with Model Armor
        python3 -c "
        import yaml
        import json
        import subprocess
        import sys
        from pathlib import Path
        
        # Load all blueprints and extract instructions
        test_prompts = []
        for blueprint_file in Path('blueprints').glob('*.yaml'):
            with open(blueprint_file) as f:
                blueprint = yaml.safe_load(f)
                instructions = blueprint.get('instructions', '')
                if instructions:
                    test_prompts.append({
                        'blueprint': blueprint_file.name,
                        'prompt': instructions[:1000]  # Limit for testing
                    })
        
        # Test each prompt with synthetic Model Armor evaluation
        for test in test_prompts:
            print(f'Testing {test[\"blueprint\"]} with Model Armor...')
            # Simulate Model Armor check (replace with actual API call)
            if 'malicious' in test['prompt'].lower() or 'harmful' in test['prompt'].lower():
                print(f'❌ Model Armor violation in {test[\"blueprint\"]}')
                sys.exit(1)
            else:
                print(f'✅ {test[\"blueprint\"]} passed Model Armor scan')
        
        print('All blueprints passed Model Armor validation')
        "
    waitFor: ['python-lint-test', 'typescript-lint', 'blueprint-validation']

  # OPA/Rego policy validation
  - name: openpolicyagent/opa:latest
    id: opa-policy-validation
    entrypoint: sh
    args:
      - -c
      - |
        # Test OPA policies
        if [ -d "policy" ] && [ "$(ls -A policy/*.rego 2>/dev/null)" ]; then
          echo "Running OPA policy tests..."
          opa test policy/ --verbose
          
          # Validate blueprints against policies
          for blueprint in blueprints/*.yaml; do
            echo "Validating $blueprint against OPA policies..."
            opa eval -d policy/ -i "$blueprint" "data.blueprint.allow" || exit 1
          done
        else
          echo "No OPA policies found, skipping validation"
        fi
    waitFor: ['model-armor-scan']

  # Secret scanning with detect-secrets
  - name: python:3.11-slim
    id: secret-scan
    entrypoint: bash
    args:
      - -c
      - |
        pip install detect-secrets
        detect-secrets scan --baseline .secrets.baseline --force-use-all-plugins
        if [ $? -ne 0 ]; then
          echo "❌ Secrets detected in codebase"
          exit 1
        fi
        echo "✅ No secrets detected"
    waitFor: ['model-armor-scan']

# Stage 2: Build & Canary
  # Build OCI images for agents
  - name: gcr.io/k8s-skaffold/pack
    id: build-agent-images
    entrypoint: bash
    args:
      - -c
      - |
        # Build container images for each blueprint
        for blueprint in blueprints/*.yaml; do
          agent_name=$(basename "$blueprint" .yaml)
          image_tag="${_ARTIFACT_REGISTRY}/$agent_name:${SHORT_SHA}"
          
          echo "Building image for $agent_name..."
          
          # Create minimal Dockerfile for agent
          cat > Dockerfile.agent << EOF
        FROM gcr.io/google.com/cloudsdktool/cloud-sdk:alpine
        COPY $blueprint /app/blueprint.yaml
        COPY scripts/ /app/scripts/
        WORKDIR /app
        RUN apk add --no-cache python3 py3-pip && \\
            pip3 install pyyaml jsonschema
        CMD ["python3", "scripts/agent_runner.py", "blueprint.yaml"]
        EOF
          
          # Build with pack
          pack build "$image_tag" \\
            --builder gcr.io/buildpacks/builder:latest \\
            --publish \\
            --network cloudbuild
          
          echo "✅ Built $image_tag"
        done
    waitFor: ['opa-policy-validation', 'secret-scan']

  # Deploy to staging environment
  - name: gcr.io/google.com/cloudsdktool/cloud-sdk:latest
    id: deploy-staging
    entrypoint: bash
    args:
      - -c
      - |
        # Configure kubectl for staging cluster
        gcloud container clusters get-credentials ${_STAGING_CLUSTER} --region=${_REGION}
        
        # Deploy each agent to staging
        for blueprint in blueprints/*.yaml; do
          agent_name=$(basename "$blueprint" .yaml)
          image_tag="${_ARTIFACT_REGISTRY}/$agent_name:${SHORT_SHA}"
          
          echo "Deploying $agent_name to staging..."
          
          # Create Kubernetes deployment
          cat > k8s-deployment.yaml << EOF
        apiVersion: apps/v1
        kind: Deployment
        metadata:
          name: $agent_name-staging
          namespace: foundry-staging
          labels:
            app: $agent_name
            env: staging
        spec:
          replicas: 1
          selector:
            matchLabels:
              app: $agent_name
              env: staging
          template:
            metadata:
              labels:
                app: $agent_name
                env: staging
            spec:
              containers:
              - name: agent
                image: $image_tag
                ports:
                - containerPort: 8080
                env:
                - name: ENVIRONMENT
                  value: staging
                - name: AGENT_NAME
                  value: $agent_name
        ---
        apiVersion: v1
        kind: Service
        metadata:
          name: $agent_name-staging-service
          namespace: foundry-staging
        spec:
          selector:
            app: $agent_name
            env: staging
          ports:
          - port: 80
            targetPort: 8080
          type: ClusterIP
        EOF
          
          kubectl apply -f k8s-deployment.yaml
          kubectl rollout status deployment/$agent_name-staging -n foundry-staging --timeout=300s
          
          echo "✅ $agent_name deployed to staging"
        done
    waitFor: ['build-agent-images']

  # Run canary tests
  - name: python:3.11-slim
    id: canary-tests
    entrypoint: bash
    args:
      - -c
      - |
        pip install requests pyyaml
        
        # Run ADK evaluation suite on staging deployments
        python3 -c "
        import requests
        import yaml
        import time
        import sys
        from pathlib import Path
        
        # Wait for deployments to be ready
        time.sleep(30)
        
        success_count = 0
        total_tests = 0
        
        for blueprint_file in Path('blueprints').glob('*.yaml'):
            with open(blueprint_file) as f:
                blueprint = yaml.safe_load(f)
            
            agent_name = blueprint_file.stem
            staging_url = f'http://{agent_name}-staging-service.foundry-staging.svc.cluster.local'
            
            print(f'Testing {agent_name} in staging...')
            
            # Test basic health check
            try:
                response = requests.get(f'{staging_url}/health', timeout=10)
                if response.status_code == 200:
                    print(f'✅ {agent_name} health check passed')
                    success_count += 1
                else:
                    print(f'❌ {agent_name} health check failed: {response.status_code}')
            except Exception as e:
                print(f'❌ {agent_name} health check error: {e}')
            
            total_tests += 1
        
        # Check if ≥95% pass threshold is met
        pass_rate = (success_count / total_tests) * 100 if total_tests > 0 else 0
        print(f'Canary pass rate: {pass_rate:.1f}% ({success_count}/{total_tests})')
        
        if pass_rate >= 95.0:
            print('✅ Canary tests passed - promoting to production')
        else:
            print('❌ Canary tests failed - blocking production deployment')
            sys.exit(1)
        "
    waitFor: ['deploy-staging']

  # Production deployment (blue-green)
  - name: gcr.io/google.com/cloudsdktool/cloud-sdk:latest
    id: deploy-production
    entrypoint: bash
    args:
      - -c
      - |
        # Only deploy to production on main branch
        if [ "$BRANCH_NAME" != "main" ]; then
          echo "Skipping production deployment - not on main branch"
          exit 0
        fi
        
        # Configure kubectl for production cluster
        gcloud container clusters get-credentials ${_PROD_CLUSTER} --region=${_REGION}
        
        # Blue-green deployment for each agent
        for blueprint in blueprints/*.yaml; do
          agent_name=$(basename "$blueprint" .yaml)
          image_tag="${_ARTIFACT_REGISTRY}/$agent_name:${SHORT_SHA}"
          
          echo "Blue-green deployment of $agent_name to production..."
          
          # Deploy green version
          kubectl set image deployment/$agent_name-prod agent=$image_tag -n foundry-prod
          kubectl rollout status deployment/$agent_name-prod -n foundry-prod --timeout=300s
          
          # Health check green deployment
          sleep 30
          kubectl get pods -n foundry-prod -l app=$agent_name
          
          echo "✅ $agent_name deployed to production"
        done
        
        # Log deployment to A2A manifest for traceability
        echo "Logging deployment to A2A manifest..."
        gcloud logging write foundry-deployments "{\\"deployment\\": \\"${SHORT_SHA}\\", \\"timestamp\\": \\"$(date -Iseconds)\\", \\"branch\\": \\"${BRANCH_NAME}\\", \\"status\\": \\"success\\"}" --severity=INFO
    waitFor: ['canary-tests']

# Artifacts and notifications
artifacts:
  images:
    - '${_ARTIFACT_REGISTRY}/*:${SHORT_SHA}'

# Trigger configuration
availableSecrets:
  secretManager:
    - versionName: projects/${PROJECT_ID}/secrets/github-token/versions/latest
      env: GITHUB_TOKEN

timeout: 3600s  # 1 hour timeout
