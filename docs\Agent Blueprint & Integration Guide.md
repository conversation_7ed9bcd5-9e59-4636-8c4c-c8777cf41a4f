Executive Snapshot
Metamorphic Foundry stores every agent as a declarative YAML “blueprint.” The same file can (1) deploy directly into Vertex AI’s managed AI Applications Flow, (2) register with the open-source Agent Development Kit (ADK) for local debugging, and (3) compile into a signed A2A package for cross-agent exchange. This guide formalizes the schema, patterns, and lifecycle so teams can spin up compliant, observable agents in minutes—not weeks. 
cloud.google.com
developers.googleblog.com
developers.googleblog.com

1 Blueprint Schema (v0.4.2)
Field	Type	Required	Notes
name	string	✓	Kebab-case; prepends image tags.
version	string	✓	SemVer; CI blocks non-semver pushes.
model	enum	✓	gemini-2.5-pro, gemini-2.5-flash, gemma-3n, external. 
cloud.google.com
economictimes.indiatimes.com
instructions	multiline string	✓	System prompt; 8 k-token max for Flash-Lite. 
cloud.google.com
tools	array<object>	—	Each object maps to ADK ToolSpec. 
google.github.io
memory	object	—	store, embedding_model, retention_days.
evaluation	object	—	Metric list + pass thresholds.
security	object	—	model_armor_policy, dlp_templates. 
cloud.google.com
medium.com
a2a	object	—	required_capabilities, token_binding=true. 
developers.googleblog.com
metadata	map<string,string>	—	Owner, squad, ticket.

A full JSONSchema file (schemas/blueprint-0.4.2.json) ships in the repo for IDE linting. 
gke-ai-labs.dev

2 Field-by-Field Best Practices
2.1 Model Selection
Gemini 2.5 Pro for >100 k tokens or code refactors; latency 4-8 s. 
timesofindia.indiatimes.com
cloud.google.com

Gemini 2.5 Flash / Flash-Lite for P95 < 1 s chat returns. 
cloud.google.com
cloud.google.com

Gemma 3n when offline, privacy-critical, or running on Pixel Edge TPU (13× speed-up with quantization). 
economictimes.indiatimes.com
developers.googleblog.com

2.2 Tool Blocks
Reference pre-built Agent Garden specs (calendar.send, sheets.append, code.search). They hydrate automatically when deployed to Flow or ADK. 
cloud.google.com

2.3 Security
Attach Model Armor presets (FRAUD_LOW, PII_MED) to every blueprint; Vertex AI auto-routes traffic through the content-safety firewall. 
cloud.google.com
cloudonair.withgoogle.com

2.4 A2A Claims
Set required_capabilities: ["memory.put", "tool.call"]; the receiving agent declines messages it cannot honor, preventing prompt-injection relays. 
developers.googleblog.com

3 End-to-End Example
yaml
Copy
Edit
name: meta-reflector
version: 0.1.3
model: gemini-2.5-pro
instructions: |
  You are Meta-Reflector, tasked with critiquing design docs…
tools:
  - type: gcp.sheets.append
    auth: service_account
memory:
  store: firestore
  embedding_model: text-embedding-gecko@001
  retention_days: 30
evaluation:
  metrics:
    - hallucination_rate <= 2%
    - toxicity_score <= 0.05
security:
  model_armor_policy: PII_MED
a2a:
  required_capabilities: ["tool.call", "memory.put"]
metadata:
  owner: ai-foundry
  ticket: FND-102
Deploy with either path:

bash
Copy
Edit
# ADK local
adk run -b blueprints/meta-reflector.yaml         # :contentReference[oaicite:13]{index=13}

# Vertex AI Applications
gcloud ai agent-blueprint deploy \
  --blueprint=blueprints/meta-reflector.yaml      # :contentReference[oaicite:14]{index=14}
4 Integration Workflows
4.1 ADK CLI → Local Debug
Hot-reload blueprints; breakpoints on tool calls.

Use adk test to execute JSON test suites before commit. 
google.github.io
developers.googleblog.com

4.2 AI Applications Flow
Import YAML via console or CLI; Flow auto-generates nodes.

Use Checkpoint / Resume for long-running jobs. 
cloud.google.com

4.3 GitOps Pipeline
PR triggers GitHub Action → JSONSchema lint.

Cloud Build validates, signs A2A manifest, pushes OCI image with blueprint tag ai-agents/<name>:<version>.

Terraform module agent_blueprint.tf applies in staging, gate-kept by Model Armor pass rate. 
gke-ai-labs.dev
medium.com

4.4 Edge Deployment (Gemma 3n)
Compile blueprint with adk edge --model=gemma-3n.

Deployed container runs on Cloud IoT or Android App Sandbox; offline memory persists to SQLite with weekly sync. 
economictimes.indiatimes.com
pureai.com

5 Memory & Retrieval Patterns
Pattern	When	How-To
Doc QA	Semi-static corpora	Vertex AI Search Grounding with automatic citations. 
cloud.google.com
Long-running chats	Conversational CRM	Firestore vectors + TTL 90 days.
Edge cache	Unreliable network	In-app SQLite, delta-sync via Cloud Tasks.

6 Evaluation Section
The evaluation block feeds ADK’s test harness and Flow’s native eval node. Recommended metrics: hallucination_rate, toxicity_score, response_latency_ms, and business-specific KPIs (e.g., conversion_lift). 
gke-ai-labs.dev
cloud.google.com

7 Versioning & Lifecycle
Immutable tags: once deployed, a given version cannot be overwritten.

Deprecation policy: blueprints < N–2 versions auto-archived after 180 days unless pinned in production.

SBOM: ADK exports an SPDX document listing tools and external calls for each build, stored alongside the blueprint image. 
cloud.google.com
cloud.google.com

8 Troubleshooting Quick Hits
Symptom	Likely Cause	Fix
“Tool not allowed” error	Missing capability in a2a claim	Add capability or adjust orchestrator mapping.
High latency spikes	Routing everything to Pro	Enable Flash/Flash-Lite auto-tier. 
cloud.google.com
Policy block 403	Model Armor violation	Tune policy level or add content filters. 
cloud.google.com
medium.com

Glossary
Blueprint – Declarative YAML file that defines an agent’s model, tools, memory, evaluation, security, and metadata.
ADK – Agent Development Kit, Google’s open-source framework for authoring and testing agents. 
developers.googleblog.com

A2A – Agent-to-Agent protocol for secure cross-agent messaging. 
developers.googleblog.com