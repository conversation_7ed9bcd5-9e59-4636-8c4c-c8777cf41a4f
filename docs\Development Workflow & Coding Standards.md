1 Repository Layout & Branching Strategy
1.1 Directory skeleton
bash
Copy
Edit
/blueprints/        # YAML agent specs
/flows/             # AI Applications Flow JSON files
/tools/             # Cloud Functions or Cloud Run micro-agents
/policy/            # Rego rules + Model Armor configs
/tests/             # ADK eval suites + unit tests
Google’s ADK quick-start follows a nearly identical layout, ensuring the CLI can hot-reload agents and tests without extra flags. 
cloud.google.com

1.2 Branching & tagging
Trunk-based with short-lived feature branches—avoids drift and speeds reviews. 
arctiq.com

Each merged PR bumps the blueprint’s version (SemVer) and tags the repo agent/<name>@<version>; Cloud Build uses the tag to label OCI images. 
cloud.google.com

2 Local Development Toolkit
2.1 ADK CLI loop
adk run -b blueprints/<file>.yaml spins up an interactive REPL, logs tool calls, and supports breakpoints—mirroring production Flow behaviour. 
developers.googleblog.com

2.2 Gemini CLI loop
For whole-repo analyses, pipe files or directories:

bash
Copy
Edit
gemini -p "@./src @./tests Explain test coverage"
The CLI streams to Gemini 2.5 Pro locally, or works offline with Gemma 3 n. 
blog.google
medium.com

2.3 Pre-commit automation
Install pre-commit and the shared config (.pre-commit-config.yaml) to run:

Black / Pyink for Python,

gts for TypeScript,

YAML schema validation,

secret scans.
Hook-based gates catch 80 % of style issues before CI. 
pre-commit.com
gatlenculp.medium.com
github.com

3 CI/CD Pipeline (Cloud Build)
3.1 Stage 0 – Lint & Unit tests
Python: pyright, pytest, Google Python Style Guide rules. 
google.github.io

TypeScript: gts check && jest. 
google.github.io

Blueprints: JSONSchema lint.

3.2 Stage 1 – Security gates
Model Armor scans synthetic prompts/responses; a failure blocks deploy. 
cloud.google.com

OPA/Rego validates blueprint policies (policy/blueprint.rego). 
openpolicyagent.org
openpolicyagent.org

3.3 Stage 2 – Build & Canary
Build OCI image gcr.io/$PROJECT/ai-agents/<name>:<version>.

Deploy to us-central1-staging via AI Applications Flow, auto-hooked to BigQuery eval dashboards. 
cloud.google.com
cloud.google.com

Canary runs ADK eval suite; on ≥ 95 % pass, promote to prod with blue-green traffic shift.

3.4 Rollbacks & audit
Failed canaries auto-roll back via Cloud Deploy; every promotion logs a signed A2A manifest for traceability. 
a2aprotocol.ai

4 Coding Standards
4.1 Python
Follow Google Python Style (PEP 8 + internal tweaks: 80-char lines, type hints, snake_case). 
google.github.io

4.2 TypeScript
Use Google’s TypeScript Style Guide enforced by gts—strict noImplicitAny, module-bound enums, and single quotes. 
google.github.io
github.com

4.3 Rego policies
One rule per file; use _test.rego companions.

“Deny-list” semantics preferred for clarity. 
openpolicyagent.org

4.4 Blueprint YAML
Max 120-char lines, UTF-8, kebab-case IDs.

Always include security.model_armor_policy and evaluation.metrics. 
cloud.google.com
developers.googleblog.com

5 Observability & Performance
Managed Prometheus sidecar in Cloud Run collects custom agent_turn_latency_seconds and token_count_total. 
cloud.google.com
cloud.google.com

Export ALL agent turns to BigQuery; Looker dashboards chart pass/fail and cost per 1 K tokens. 
arctiq.com

6 Edge & Offline Workflow (Gemma 3 n)
Compile with adk edge --model=gemma-3n; package uses Per-Layer Embeddings for 2 GB RAM devices. 
developers.googleblog.com

Weekly delta-sync pushes new policies and embeddings when connectivity returns. 
developers.googleblog.com

7 Governance Automation
Rego unit tests run in CI (opa test policy/...). 
openpolicyagent.org

Quarterly policy review scheduled; Model Armor presets tuned to latest Google Cloud threat intel. 
cloud.google.com

8 Developer Checklist
git checkout -b feature/<ticket>

Write code + blueprint; run pre-commit.

adk test + gemini prompt for sanity.

Open PR; address CI lint/test feedback.

Merge → Cloud Build pipeline → Staging canary.

Confirm Looker dashboards; prod auto-promotes.

Tag notes in CHANGELOG.

Follow the checklist and Metamorphic Foundry will keep shipping agents that are fast, safe, and easy to debug.