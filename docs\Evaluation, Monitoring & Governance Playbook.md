1 Purpose & Scope
This guide applies to all workloads that run through Metamorphic Foundry: Vertex AI chat endpoints (Gemini 2.5 Pro/Flash), Cloud Run micro-agents, and on-device Gemma 3n edge bundles. It covers:

Evaluation (pre-merge, canary, continuous)

Monitoring & alerting (Managed Prometheus → BigQuery → Looker)

Governance (content safety, policy-as-code, audit logging, regulatory watch)

2 Metric Taxonomy
Category	Metric	Source	Target
Quality	Hallucination Rate	Vertex AI generative-AI evaluation service 
cloud.google.com
≤ 2 %
Safety	Toxicity Score (0-1)	Model Armor 
cloud.google.com
≤ 0.05
Perf.	P95 Latency	Managed Prometheus scrape 
cloud.google.com
Flash < 1 s / Pro < 8 s
Cost	Tokens per $	BigQuery usage view 
cloud.google.com
Trending ↑ 10 % Q-over-Q

Gemma 3n edge agents inherit identical KPIs plus on-device CPU % and battery drain—benchmarked in the model card’s evaluation section 
ai.google.dev
.

3 Evaluation Workflow
3.1 Pre-Merge (CI)
Synthetic prompt set → Vertex AI evaluation API (hallucination & safety). CI fails on threshold breach. 
cloud.google.com

Unit tests in ADK harness assert tool behavior and Rego policy expectations.

3.2 Staging Canary
Cloud Build deploys blueprint to us-central1-staging; 500 live requests replay from production shadow traffic.

Canary passes when ≥ 95 % requests hit success thresholds.

3.3 Continuous Prod Eval
Nightly BigQuery job pulls 1 % stratified sample, re-scores with Vertex AI evaluation.

Looker Studio dashboards refresh every morning; chart customizations shipped in May-2025 release make threshold bands clearer 
cloud.google.com
.

4 Monitoring & Telemetry Architecture
css
Copy
Edit
Agent → OpenTelemetry exporter → Managed Prometheus  →  Cloud Monitoring alerts
                      │
                      └── Pub/Sub → Dataflow → BigQuery (agent_turn table) → Looker
Managed Prometheus auto-collects agent_turn_latency_seconds and token_count_total without running your own Prometheus farm 
cloud.google.com
.

BigQuery retains per-turn JSON (prompt, response hash, policy verdict) for 400 days—as recommended by Vertex AI audit-logging docs 
cloud.google.com
.

Looker Studio templates delivered at NEXT ’25 show top offenders by hallucination and cost 
googlecloudcommunity.com
.

5 Governance & Policy Controls
5.1 Content Safety – Model Armor
Model Armor scans every prompt/response for PII, jailbreak attempts, hate, and self-harm content; blocking or redacting as configured 
cloud.google.com
. Integration is now first-class inside Vertex AI since NEXT ’25 
cloud.google.com
.

5.2 Policy-as-Code – Open Policy Agent
Rego policies (policy/blueprint.rego) validate blueprint fields (e.g., every agent must set security.model_armor_policy). OPA docs provide syntax and testing best practices 
openpolicyagent.org
.

OPA test suite runs in CI; failures block merge and deployment.

5.3 Secure Messaging – A2A Protocol
All cross-agent calls include token-bound mTLS and capability claims per the April-2025 A2A spec 
developers.googleblog.com
linuxfoundation.org
.

5.4 Regulatory Watch
EU AI Act: expect compliance guidance only at end-2025; keep provisional risk assessments until Code of Practice finalizes 
reuters.com
.

Google’s updated Responsible AI principles (Mar 2025) emphasize human oversight and unintended-harm mitigation, replacing prior blanket bans 
wired.com
.

6 Alerting & Incident Response
Alert	Threshold	Action
Safety Violation	Model Armor severity ≥ HIGH 5× in 5 min	Auto-isolate agent; page on-call.
Latency SLO	P95 latency > target for 10 min	Auto-route to Flash-Lite (if on Pro) and raise Cloud Monitoring ticket.
Hallucination Spike	Eval sample hallucination > 4 %	Trigger rollback to last known-good blueprint; open Jira root-cause task.

Playbooks live in runbooks/ and link to BigQuery incident deep-dive queries.

7 Dashboard Templates
Looker Studio pack includes:

Executive Overview – SLA, cost, safety pass-rate.

Latency & Load – heatmap of P50/P95 by region.

Policy Analytics – Model Armor violations by category.

Compliance – audit-log query widgets answering “who did what, when, where.” Templates leverage the new viewer-consent flow for BigQuery connectors (May 22 2025) 
cloud.google.com
.

8 Retention & Archiving
Raw Logs (BigQuery): 400‐day default; export to Cloud Storage for cold archive after. 
cloud.google.com

Prometheus Metrics: 13 months via Managed Service defaults.

Blueprint Images: two latest minor versions kept per agent; older tagged images stored in Artifact Registry with “cold” tier.

9 Developer Checklist
Add evaluation YAML → at least hallucination & toxicity metrics.

Write Rego tests for new policy rules.

Run adk test + opa test locally.

Push PR; ensure CI passes Model Armor synthetic scan.

Verify Looker canary dashboard after staging deploy.

Set PagerDuty escalation for new alert rules if metric added.

Document runbook link in README.md.

Glossary
Model Armor – Google Cloud managed firewall for LLM prompts and responses. 
cloud.google.com

A2A Protocol – Secure, capability-scoped agent-to-agent messaging standard. 
developers.googleblog.com

Managed Prometheus – Fully-managed, multi-cloud Prometheus compatible service. 
cloud.google.com

OPA/Rego – Open-source policy engine and declarative language for policy-as-code. 
openpolicyagent.org

Metamorphic Foundry now has a closed-loop safety and quality system: every agent is evaluated before merge, watched in real time, and fenced by automated policies. Plug this playbook into your CI/CD and you can ship new shape-shifting agents with confidence—and sleep through the night while they work.