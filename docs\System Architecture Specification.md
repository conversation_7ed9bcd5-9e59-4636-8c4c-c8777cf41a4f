1 Overview & Purpose
Metamorphic Foundry is a cloud-native “agent factory” that turns YAML blueprints into live, policy-governed AI agents in < 30 minutes. It rests on Google Cloud’s new AI Applications Flow for orchestration, the open Agent-to-Agent (A2A) protocol for secure messaging, and a tiered model pool spanning Gemini 2.5 Pro / Flash / Flash-Lite in the cloud and Gemma 3n at the edge. 
cloud.google.com
developers.googleblog.com
blog.google
economictimes.indiatimes.com

2 Architectural Principles
Principle	Rationale
Plan-first, small-step execution	Minimizes blast radius and supports rollback with Flow checkpoints. 
cloud.google.com
Model tiering	Route latency-sensitive calls to <PERSON>/Flash-Lite; heavy-context jobs to Pro; offline to Gemma 3n. 
blog.google
economictimes.indiatimes.com
Interoperability by default	A2A guarantees any agent—internal or 3rd-party—can exchange signed JSON messages. 
developers.googleblog.com
Built-in governance	Model Armor and BigQuery audit logs are non-optional in every environment. 
cloud.google.com
cloud.google.com

3 High-Level Component Diagram (text)
css
Copy
Edit
┌────────── UI/API Gateway ──────────┐
│  • GraphQL (Apollo)                │
│  • REST (Cloud Endpoints)          │
└──────────────┬─────────────────────┘
               ▼
┌──────── AI Applications Flow ──────┐
│  Orchestrator & Task Router        │
│  ↔ A2A Secure Messaging Layer      │
└──────────────┬─────────────────────┘
   YAML Blueprints ▼            ▲ CI/CD Webhooks
┌──────── Agent Runtime ────────┐
│  • Vertex AI Chat Endpoints   │
│  • Cloud Run Micro-Agents      │
│  • Gemma 3n Edge Pods          │
└──────────────┬─────────────────────┘
               ▼
┌────── Memory & RAG Layer ──────┐
│  • Firestore Vector Store       │
│  • Vertex AI Search Grounding   │
└──────────────┬──────────────────┘
               ▼
┌────── Observability Stack ──────┐
│  • BigQuery Turn Logs           │
│  • Looker Dashboards            │
│  • Cloud Monitoring + Prometheus│
└─────────────────────────────────┘
4 Layer-by-Layer Specification
4.1 Interface Layer
GraphQL + REST expose a uniform contract to web apps, CLIs, and other agents.

Auth via Google Identity-Aware Proxy; service-to-service via workload identity federation.

4.2 Orchestrator — AI Applications Flow
Drag-and-drop JSON Flow graphs or declarative YAML.

Built-in “Function Call” nodes map to Cloud Functions or external APIs.

Checkpoint/Resume lets long-running chains survive instance restarts. 
cloud.google.com

4.3 Messaging — A2A Protocol
Token-bound mTLS handshake with per-hop re-signing.

Embedded capability claims ensure the receiving agent can only invoke approved tools. 
developers.googleblog.com

4.4 Runtime Tiering
Tier	Deployment	Typical Tasks
Gemini 2.5 Pro	Vertex AI endpoint	Whole-doc reasoning, code refactors. 
cloud.google.com
Gemini 2.5 Flash / Flash-Lite	Vertex AI endpoint	< 1 s chat, routing, quick classify. 
blog.google
Gemma 3n	Edge TPU / Android Neural API	Offline vision-language tasks. 
economictimes.indiatimes.com
Micro-Agents	Cloud Run containers	Specialized tool-calling wrappers; benefit from scale-to-zero. 
youtube.com

4.5 Memory & Retrieval
Firestore hosts vector embeddings; pluggable swap to Cloud SQL Postgres + pgvector for relational projects.

Vertex AI Search Grounding injects enterprise docs with citation IDs. 
medium.com

4.6 Observability & Telemetry
Every agent turn streams to BigQuery; Looker templates visualize latency, token spend, policy blocks. 
cloud.google.com

Managed Prometheus scrapes Cloud Run metrics; Cloud Monitoring sets SLO alerts. 
cloud.google.com

4.7 Security & Governance
Model Armor scans prompts/responses for PII, jailbreaks, self-harm, and hate content. 
cloud.google.com

VPC-only endpoints; Direct VPC Egress chosen over connectors to cut compute charges. 
cloud.google.com

Assured Workloads enabled for FedRAMP/HIPAA clients.

5 Technology Choices & Rationale
Concern	Selected Tool	Why
Agent authoring	ADK + Agent Garden	Rapid prototypes, sample agents, and debugger. 
developers.googleblog.com
cloud.google.com
Multi-agent scaling	AI Applications Flow	Native function calling, JSON planner, checkpointing. 
cloud.google.com
Interop	A2A	Open, vendor-agnostic, partner ecosystem of ≥ 50 companies. 
developers.googleblog.com
Edge inference	Gemma 3n	2 GB RAM footprint, 13× TPU speed-up. 
economictimes.indiatimes.com
Compliance	Model Armor + BigQuery immutable logs	Built-in policy engine + auditability. 
cloud.google.com
cloud.google.com

6 Deployment Topology
6.1 Environments
Environment	Infra	Notes
Dev (Local)	Gemini CLI → Vertex AI; Docker Compose Gemma 3n simulator.	Hot-reload Flow JSON.
Staging	Cloud Run + low-cost Flash-Lite models.	Canary tests, chaos experiments. 
blog.google
Prod	Multi-region Vertex AI + regional Cloud Run; Cloud Build triggers IaC.	Blue-green releases with 5-minute cut-over.

6.2 CI/CD Pipeline
GitHub Actions → Cloud Build → Terraform; each commit validates YAML schema, spins ephemeral Flow, runs ADK e2e tests. 
developers.googleblog.com
cloud.google.com

7 Scalability & Performance
Auto-scaling Cloud Run instances per micro-agent; P95 cold-start < 400 ms in us-central1. 
youtube.com

Flash-Lite delivers median 350 ms responses at 30 % cheaper per 1K tokens than Flash. 
blog.google

Edge pods cache embeddings to avoid network hops when offline.

8 Cost Optimization
Direct VPC Egress removes per-VM compute fees vs. Serverless VPC connectors. 
cloud.google.com

Tiered model routing targets ≥ 40 % of calls to Flash/Flash-Lite or Gemma 3n.

Scheduled BigQuery partition pruning caps storage.

9 Reliability & Resilience
Each Flow node retries with exponential back-off; idempotent tool calls use Cloud Tasks for deduplication.

Multi-region Vertex AI endpoints with automatic failover.

Disaster recovery: daily Terraform state backup + Cloud SQL point-in-time restore.

10 Compliance & Governance
DLP templates mask credit-card & HIPAA entities before persistence.

Audit log retention: 400 days in BigQuery per enterprise default.

Policy-as-code: Rego rules (Open Policy Agent) validate blueprints pre-deploy.

11 Open Problems & Future R&D
Dynamic plan-first scheduling—auto-decide number & roles of agents per request.

Streaming multimodal reasoning once Project Astra APIs reach GA. 
techcrunch.com

On-device federated learning for Gemma 3n personalization without data egress.

12 Glossary
AI Applications Flow – Managed orchestration canvas inside Vertex AI. 
cloud.google.com

A2A Protocol – Open spec for secure agent messaging. 
developers.googleblog.com

ADK (Agent Development Kit) – OSS framework for building & debugging multi-agent systems. 
developers.googleblog.com

Model Armor – Content-safety firewall for LLM prompts/responses. 
cloud.google.com