# Metamorphic Foundry Flow Compiler

The Flow Compiler is a core component of the Metamorphic Foundry platform that converts agent blueprints into executable AI Applications Flow definitions. It implements the YAML → Flow compiler architecture with performance targets and observability integration.

## Overview

The Flow Compiler transforms declarative agent blueprints into executable flow graphs that can be deployed and executed at scale. It provides:

- **Blueprint Compilation**: Convert YAML/JSON blueprints to AI Applications Flow
- **Flow Execution**: Execute compiled flows with performance monitoring
- **Observability Integration**: Built-in metrics collection and logging
- **Security Enforcement**: Model Armor and policy validation
- **Performance Optimization**: Model-specific optimizations and routing

## Architecture

```
Blueprint (YAML/JSON) → Flow Compiler → AI Applications Flow → Flow Executor → Response
                                    ↓
                              Observability Pipeline
```

### Flow Components

1. **Input Processor**: Validates and preprocesses user input
2. **Model Armor**: Content safety and security filtering
3. **Memory Retrieval**: Context and conversation history
4. **LLM Processor**: Core language model processing
5. **Tool Executors**: External tool and API integrations
6. **Response Processor**: Output formatting and validation
7. **Response Evaluator**: Quality and compliance checking
8. **Output Formatter**: Final response formatting
9. **Observability Collector**: Metrics and logging

## Installation

```bash
cd flow_compiler
pip install -r requirements.txt
```

## Usage

### Command Line Interface

```bash
# Compile blueprint to flow
python cli.py compile agent-blueprint.yaml --output agent-flow.json

# Execute a compiled flow
python cli.py execute agent-flow.json --input '{"user_message": "Hello"}'

# Compile and execute in one step
python cli.py run agent-blueprint.yaml --input input.json

# Validate a flow definition
python cli.py validate agent-flow.json

# Generate flow visualization
python cli.py visualize agent-flow.json --output flow-diagram.html
```

### Python API

```python
from blueprint_to_flow import compile_blueprint_to_flow
from flow_executor import execute_flow, ExecutionContext
import asyncio

# Compile blueprint
flow = compile_blueprint_to_flow("agent-blueprint.yaml", "agent-flow.json")

# Execute flow
context = ExecutionContext(
    execution_id="demo-123",
    flow_name=flow.name,
    flow_version=flow.version
)

inputs = {"user_message": "Hello, I need help"}
result = await execute_flow(flow.to_dict(), inputs, "project-id", context)

print(f"Response: {result['output']}")
```

## Blueprint to Flow Mapping

### Input Blueprint
```yaml
name: customer-service
model: gemini-2.5-flash
instructions: "You are a helpful customer service agent..."
tools:
  - type: customer_db.lookup
  - type: ticket_system.create
security:
  model_armor_policy: PII_MED
memory:
  store: firestore
  retention_days: 30
```

### Generated Flow
```json
{
  "name": "customer-service-flow",
  "nodes": [
    {"id": "node_001", "type": "input_processor"},
    {"id": "node_002", "type": "model_armor"},
    {"id": "node_003", "type": "memory_retrieval"},
    {"id": "node_004", "type": "llm_processor"},
    {"id": "node_005", "type": "tool_executor"},
    {"id": "node_006", "type": "response_processor"},
    {"id": "node_007", "type": "output_formatter"}
  ],
  "connections": [
    {"source_node": "node_001", "target_node": "node_002"},
    {"source_node": "node_002", "target_node": "node_003"}
  ]
}
```

## Performance Targets

The Flow Compiler optimizes for the following performance targets:

| Model | Latency Target | Throughput | Cost Efficiency |
|-------|---------------|------------|-----------------|
| Gemini 2.5 Flash | < 1s P95 | High | Optimized |
| Gemini 2.5 Pro | < 8s P95 | Medium | Quality-focused |
| Gemini 2.5 Flash Lite | < 500ms P95 | Very High | Cost-optimized |
| Gemma 3n | < 2s P95 | High | Edge-optimized |

## Node Types

### Input Processor
- Validates user input
- Applies preprocessing rules
- Rate limiting and security checks

### Model Armor
- Content safety filtering
- PII detection and redaction
- Policy enforcement

### Memory Retrieval
- Vector similarity search
- Conversation history
- Context augmentation

### LLM Processor
- Model-specific optimizations
- Function calling support
- Safety settings enforcement

### Tool Executor
- External API integration
- Authentication handling
- Error recovery and retries

### Response Processor
- Output formatting
- Citation generation
- Response validation

### Response Evaluator
- Hallucination detection
- Toxicity scoring
- Quality assessment

### Output Formatter
- JSON/text formatting
- Metadata inclusion
- Response sanitization

### Observability Collector
- Metrics aggregation
- Pub/Sub publishing
- BigQuery integration

## Configuration

### Model-Specific Settings

```python
model_configs = {
    "gemini-2.5-flash": {
        "temperature": 0.7,
        "max_tokens": 8192,
        "timeout_ms": 5000
    },
    "gemini-2.5-pro": {
        "temperature": 0.7,
        "max_tokens": 32768,
        "timeout_ms": 30000
    }
}
```

### Security Policies

```python
security_config = {
    "model_armor_policy": "PII_MED",
    "dlp_templates": ["EMAIL_ADDRESS", "PHONE_NUMBER"],
    "block_on_violation": True
}
```

### Observability Settings

```python
observability_config = {
    "pubsub_topic": "projects/{project}/topics/agent-turns",
    "bigquery_dataset": "foundry_observability",
    "sampling_rate": 1.0
}
```

## Examples

### Basic Agent Flow

```python
# Run the end-to-end example
python examples/end_to_end_example.py
```

This example demonstrates:
- Blueprint creation and compilation
- Flow execution with multiple scenarios
- Performance monitoring
- Error handling
- Observability integration

### Custom Node Implementation

```python
async def custom_node_executor(node, inputs, context):
    """Custom node executor implementation"""
    config = node["config"]
    
    # Process inputs
    result = await process_custom_logic(inputs, config)
    
    return {
        "output": result,
        "metrics": {
            "processing_time_ms": 100,
            "success": True
        }
    }

# Register custom executor
executor.node_executors["custom_type"] = custom_node_executor
```

## Monitoring and Observability

The Flow Compiler provides comprehensive observability:

### Execution Metrics
- Total execution time
- Per-node execution time
- Token usage and costs
- Error rates and types

### Quality Metrics
- Hallucination rates
- Toxicity scores
- User satisfaction
- Response relevance

### Performance Metrics
- Latency percentiles
- Throughput rates
- Resource utilization
- Cost per execution

### Integration with Foundry Platform

The Flow Compiler integrates with:
- **BigQuery**: For metrics storage and analytics
- **Pub/Sub**: For real-time event streaming
- **Looker**: For dashboard visualization
- **Cloud Monitoring**: For alerting and SLOs

## Development

### Running Tests

```bash
python -m pytest tests/
```

### Code Quality

```bash
black .
flake8 .
mypy .
```

### Contributing

1. Follow the blueprint schema specifications
2. Maintain performance targets
3. Include comprehensive tests
4. Update documentation

## Troubleshooting

### Common Issues

**Compilation Errors**
- Verify blueprint schema compliance
- Check required fields are present
- Validate tool configurations

**Execution Failures**
- Check node dependencies
- Verify input/output connections
- Review error logs

**Performance Issues**
- Monitor execution metrics
- Check model selection appropriateness
- Review tool timeout settings

### Debug Mode

```bash
python cli.py --verbose execute flow.json --input input.json
```

## License

Apache License 2.0 - see LICENSE file for details.
