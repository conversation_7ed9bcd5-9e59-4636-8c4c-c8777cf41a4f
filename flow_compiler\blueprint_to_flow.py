"""
Metamorphic Foundry Blueprint to AI Applications Flow Compiler
Converts agent blueprints to executable AI Applications Flow definitions
"""

import json
import yaml
import uuid
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
from pathlib import Path
import logging

logger = logging.getLogger(__name__)


@dataclass
class FlowNode:
    """Represents a node in the AI Applications Flow"""
    id: str
    type: str
    config: Dict[str, Any]
    inputs: List[str] = None
    outputs: List[str] = None
    position: Dict[str, int] = None
    
    def __post_init__(self):
        if self.inputs is None:
            self.inputs = []
        if self.outputs is None:
            self.outputs = []
        if self.position is None:
            self.position = {"x": 0, "y": 0}


@dataclass
class FlowConnection:
    """Represents a connection between flow nodes"""
    id: str
    source_node: str
    source_output: str
    target_node: str
    target_input: str


@dataclass
class AIApplicationsFlow:
    """Complete AI Applications Flow definition"""
    name: str
    version: str
    description: str
    nodes: List[FlowNode]
    connections: List[FlowConnection]
    metadata: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert flow to dictionary"""
        return {
            "name": self.name,
            "version": self.version,
            "description": self.description,
            "nodes": [asdict(node) for node in self.nodes],
            "connections": [asdict(conn) for conn in self.connections],
            "metadata": self.metadata
        }
    
    def to_json(self) -> str:
        """Convert flow to JSON string"""
        return json.dumps(self.to_dict(), indent=2)
    
    def save(self, filepath: Path) -> None:
        """Save flow to file"""
        with open(filepath, 'w') as f:
            if filepath.suffix == '.json':
                f.write(self.to_json())
            elif filepath.suffix in ['.yaml', '.yml']:
                yaml.dump(self.to_dict(), f, default_flow_style=False)
            else:
                raise ValueError("File must have .json, .yaml, or .yml extension")


class BlueprintToFlowCompiler:
    """Compiles agent blueprints to AI Applications Flow definitions"""
    
    def __init__(self):
        self.node_counter = 0
        self.connection_counter = 0
    
    def compile(self, blueprint: Dict[str, Any]) -> AIApplicationsFlow:
        """Compile blueprint to AI Applications Flow"""
        
        logger.info(f"Compiling blueprint: {blueprint.get('name', 'unknown')}")
        
        # Reset counters
        self.node_counter = 0
        self.connection_counter = 0
        
        # Create flow structure
        flow_name = f"{blueprint['name']}-flow"
        flow_version = blueprint.get('version', '1.0.0')
        flow_description = f"AI Applications Flow for {blueprint['name']} agent"
        
        nodes = []
        connections = []
        
        # 1. Create input node
        input_node = self._create_input_node()
        nodes.append(input_node)
        
        # 2. Create model armor node (if security configured)
        model_armor_node = None
        if blueprint.get('security', {}).get('model_armor_policy'):
            model_armor_node = self._create_model_armor_node(blueprint['security'])
            nodes.append(model_armor_node)
            
            # Connect input to model armor
            connections.append(self._create_connection(
                input_node.id, "user_message",
                model_armor_node.id, "content"
            ))
        
        # 3. Create memory retrieval node (if memory configured)
        memory_node = None
        if blueprint.get('memory'):
            memory_node = self._create_memory_node(blueprint['memory'])
            nodes.append(memory_node)
            
            # Connect to memory
            source_node = model_armor_node if model_armor_node else input_node
            connections.append(self._create_connection(
                source_node.id, "approved_content" if model_armor_node else "user_message",
                memory_node.id, "query"
            ))
        
        # 4. Create LLM node
        llm_node = self._create_llm_node(blueprint)
        nodes.append(llm_node)
        
        # Connect to LLM
        source_node = memory_node if memory_node else (model_armor_node if model_armor_node else input_node)
        source_output = "context" if memory_node else ("approved_content" if model_armor_node else "user_message")
        connections.append(self._create_connection(
            source_node.id, source_output,
            llm_node.id, "user_input"
        ))
        
        # 5. Create tool nodes (if tools configured)
        tool_nodes = []
        if blueprint.get('tools'):
            for tool_config in blueprint['tools']:
                tool_node = self._create_tool_node(tool_config)
                tool_nodes.append(tool_node)
                nodes.append(tool_node)
                
                # Connect LLM to tool
                connections.append(self._create_connection(
                    llm_node.id, "tool_calls",
                    tool_node.id, "function_call"
                ))
        
        # 6. Create response processing node
        response_node = self._create_response_node(blueprint)
        nodes.append(response_node)
        
        # Connect to response processor
        if tool_nodes:
            # If tools exist, aggregate their outputs
            for tool_node in tool_nodes:
                connections.append(self._create_connection(
                    tool_node.id, "result",
                    response_node.id, "tool_results"
                ))
        
        connections.append(self._create_connection(
            llm_node.id, "response",
            response_node.id, "llm_response"
        ))
        
        # 7. Create evaluation node (if evaluation configured)
        eval_node = None
        if blueprint.get('evaluation'):
            eval_node = self._create_evaluation_node(blueprint['evaluation'])
            nodes.append(eval_node)
            
            connections.append(self._create_connection(
                response_node.id, "final_response",
                eval_node.id, "response"
            ))
        
        # 8. Create output node
        output_node = self._create_output_node()
        nodes.append(output_node)
        
        # Connect to output
        source_node = eval_node if eval_node else response_node
        connections.append(self._create_connection(
            source_node.id, "validated_response" if eval_node else "final_response",
            output_node.id, "response"
        ))
        
        # 9. Create observability node
        observability_node = self._create_observability_node(blueprint)
        nodes.append(observability_node)
        
        # Connect observability to all major nodes
        for node in [input_node, llm_node, response_node, output_node]:
            connections.append(self._create_connection(
                node.id, "metrics",
                observability_node.id, "metrics_data"
            ))
        
        # Create flow metadata
        metadata = {
            "blueprint_name": blueprint['name'],
            "blueprint_version": blueprint['version'],
            "model": blueprint['model'],
            "compiled_at": "2024-01-01T00:00:00Z",  # Would use actual timestamp
            "compiler_version": "1.0.0",
            "performance_targets": {
                "hallucination_rate": "≤ 2%",
                "toxicity_score": "≤ 0.05",
                "latency_p95": self._get_latency_target(blueprint['model'])
            },
            "security_level": blueprint.get('security', {}).get('model_armor_policy', 'NONE'),
            "tools_count": len(blueprint.get('tools', [])),
            "memory_enabled": bool(blueprint.get('memory')),
            "a2a_enabled": bool(blueprint.get('a2a'))
        }
        
        return AIApplicationsFlow(
            name=flow_name,
            version=flow_version,
            description=flow_description,
            nodes=nodes,
            connections=connections,
            metadata=metadata
        )
    
    def _create_input_node(self) -> FlowNode:
        """Create input processing node"""
        return FlowNode(
            id=self._next_node_id(),
            type="input_processor",
            config={
                "input_validation": True,
                "rate_limiting": {
                    "requests_per_minute": 100,
                    "burst_size": 10
                },
                "preprocessing": {
                    "trim_whitespace": True,
                    "normalize_unicode": True,
                    "max_length": 10000
                }
            },
            outputs=["user_message", "metadata", "metrics"],
            position={"x": 100, "y": 100}
        )
    
    def _create_model_armor_node(self, security_config: Dict[str, Any]) -> FlowNode:
        """Create Model Armor security node"""
        return FlowNode(
            id=self._next_node_id(),
            type="model_armor",
            config={
                "policy": security_config.get('model_armor_policy', 'PII_LOW'),
                "dlp_templates": security_config.get('dlp_templates', []),
                "block_on_violation": True,
                "log_violations": True
            },
            inputs=["content"],
            outputs=["approved_content", "violation_details", "metrics"],
            position={"x": 300, "y": 100}
        )
    
    def _create_memory_node(self, memory_config: Dict[str, Any]) -> FlowNode:
        """Create memory retrieval node"""
        return FlowNode(
            id=self._next_node_id(),
            type="memory_retrieval",
            config={
                "store_type": memory_config.get('store', 'firestore'),
                "retention_days": memory_config.get('retention_days', 30),
                "similarity_threshold": 0.7,
                "max_results": 5,
                "embedding_model": "text-embedding-004"
            },
            inputs=["query"],
            outputs=["context", "retrieved_memories", "metrics"],
            position={"x": 500, "y": 100}
        )
    
    def _create_llm_node(self, blueprint: Dict[str, Any]) -> FlowNode:
        """Create LLM processing node"""
        model = blueprint['model']
        
        # Model-specific configurations
        model_configs = {
            "gemini-2.5-flash": {
                "temperature": 0.7,
                "max_tokens": 8192,
                "top_p": 0.9,
                "timeout_ms": 5000
            },
            "gemini-2.5-pro": {
                "temperature": 0.7,
                "max_tokens": 32768,
                "top_p": 0.9,
                "timeout_ms": 30000
            },
            "gemini-2.5-flash-lite": {
                "temperature": 0.7,
                "max_tokens": 4096,
                "top_p": 0.9,
                "timeout_ms": 2000
            },
            "gemma-3n": {
                "temperature": 0.7,
                "max_tokens": 8192,
                "top_p": 0.9,
                "timeout_ms": 3000
            }
        }
        
        return FlowNode(
            id=self._next_node_id(),
            type="llm_processor",
            config={
                "model": model,
                "instructions": blueprint['instructions'],
                "model_config": model_configs.get(model, model_configs["gemini-2.5-flash"]),
                "function_calling": bool(blueprint.get('tools')),
                "streaming": False,
                "safety_settings": {
                    "harassment": "BLOCK_MEDIUM_AND_ABOVE",
                    "hate_speech": "BLOCK_MEDIUM_AND_ABOVE",
                    "sexually_explicit": "BLOCK_MEDIUM_AND_ABOVE",
                    "dangerous_content": "BLOCK_MEDIUM_AND_ABOVE"
                }
            },
            inputs=["user_input", "context", "tool_results"],
            outputs=["response", "tool_calls", "usage_metadata", "metrics"],
            position={"x": 700, "y": 100}
        )
    
    def _create_tool_node(self, tool_config: Dict[str, Any]) -> FlowNode:
        """Create tool execution node"""
        tool_type = tool_config.get('type', 'unknown')
        
        return FlowNode(
            id=self._next_node_id(),
            type="tool_executor",
            config={
                "tool_type": tool_type,
                "tool_config": tool_config,
                "timeout_ms": 10000,
                "retry_attempts": 3,
                "auth_method": tool_config.get('auth', 'none')
            },
            inputs=["function_call"],
            outputs=["result", "error", "metrics"],
            position={"x": 900, "y": 200 + (self.node_counter * 100)}
        )
    
    def _create_response_node(self, blueprint: Dict[str, Any]) -> FlowNode:
        """Create response processing node"""
        return FlowNode(
            id=self._next_node_id(),
            type="response_processor",
            config={
                "format_response": True,
                "include_citations": bool(blueprint.get('tools')),
                "max_response_length": 4000,
                "response_templates": {
                    "error": "I apologize, but I encountered an error processing your request.",
                    "timeout": "I'm taking longer than expected. Please try again.",
                    "blocked": "I cannot process this request due to safety policies."
                }
            },
            inputs=["llm_response", "tool_results"],
            outputs=["final_response", "response_metadata", "metrics"],
            position={"x": 1100, "y": 100}
        )
    
    def _create_evaluation_node(self, eval_config: Dict[str, Any]) -> FlowNode:
        """Create evaluation node"""
        return FlowNode(
            id=self._next_node_id(),
            type="response_evaluator",
            config={
                "metrics": eval_config.get('metrics', []),
                "hallucination_detector": {
                    "enabled": True,
                    "threshold": 0.02
                },
                "toxicity_detector": {
                    "enabled": True,
                    "threshold": 0.05
                },
                "quality_scorer": {
                    "enabled": True,
                    "min_score": 0.7
                }
            },
            inputs=["response", "original_query"],
            outputs=["validated_response", "evaluation_scores", "metrics"],
            position={"x": 1300, "y": 100}
        )
    
    def _create_output_node(self) -> FlowNode:
        """Create output node"""
        return FlowNode(
            id=self._next_node_id(),
            type="output_formatter",
            config={
                "format": "json",
                "include_metadata": True,
                "include_timing": True,
                "sanitize_output": True
            },
            inputs=["response", "metadata"],
            outputs=["formatted_response", "metrics"],
            position={"x": 1500, "y": 100}
        )
    
    def _create_observability_node(self, blueprint: Dict[str, Any]) -> FlowNode:
        """Create observability node"""
        return FlowNode(
            id=self._next_node_id(),
            type="observability_collector",
            config={
                "agent_name": blueprint['name'],
                "agent_version": blueprint['version'],
                "model": blueprint['model'],
                "pubsub_topic": "projects/{project_id}/topics/agent-turns",
                "bigquery_dataset": "foundry_observability",
                "metrics_collection": {
                    "latency": True,
                    "token_usage": True,
                    "cost_tracking": True,
                    "quality_scores": True,
                    "error_rates": True
                },
                "sampling_rate": 1.0  # 100% sampling for development
            },
            inputs=["metrics_data"],
            outputs=["observability_status"],
            position={"x": 800, "y": 400}
        )
    
    def _create_connection(self, source_node: str, source_output: str, 
                          target_node: str, target_input: str) -> FlowConnection:
        """Create connection between nodes"""
        return FlowConnection(
            id=self._next_connection_id(),
            source_node=source_node,
            source_output=source_output,
            target_node=target_node,
            target_input=target_input
        )
    
    def _next_node_id(self) -> str:
        """Generate next node ID"""
        self.node_counter += 1
        return f"node_{self.node_counter:03d}"
    
    def _next_connection_id(self) -> str:
        """Generate next connection ID"""
        self.connection_counter += 1
        return f"conn_{self.connection_counter:03d}"
    
    def _get_latency_target(self, model: str) -> str:
        """Get latency target for model"""
        targets = {
            "gemini-2.5-flash": "< 1s P95",
            "gemini-2.5-pro": "< 8s P95",
            "gemini-2.5-flash-lite": "< 500ms P95",
            "gemma-3n": "< 2s P95"
        }
        return targets.get(model, "< 5s P95")


def compile_blueprint_to_flow(blueprint_path: Path, output_path: Path = None) -> AIApplicationsFlow:
    """Compile blueprint file to AI Applications Flow"""
    
    # Load blueprint
    with open(blueprint_path, 'r') as f:
        if blueprint_path.suffix == '.json':
            blueprint = json.load(f)
        elif blueprint_path.suffix in ['.yaml', '.yml']:
            blueprint = yaml.safe_load(f)
        else:
            raise ValueError("Blueprint file must be JSON or YAML")
    
    # Compile to flow
    compiler = BlueprintToFlowCompiler()
    flow = compiler.compile(blueprint)
    
    # Save if output path provided
    if output_path:
        flow.save(output_path)
        logger.info(f"Flow saved to: {output_path}")
    
    return flow
