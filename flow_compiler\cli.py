#!/usr/bin/env python3
"""
Flow Compiler CLI
Command-line interface for blueprint compilation and flow execution
"""

import click
import json
import yaml
import asyncio
import sys
from pathlib import Path
from typing import Dict, Any

from blueprint_to_flow import compile_blueprint_to_flow, BlueprintToFlowCompiler
from flow_executor import execute_flow, ExecutionContext


@click.group()
@click.option('--verbose', '-v', is_flag=True, help='Verbose output')
@click.pass_context
def cli(ctx, verbose: bool):
    """Metamorphic Foundry Flow Compiler CLI"""
    ctx.ensure_object(dict)
    ctx.obj['verbose'] = verbose
    
    if verbose:
        import logging
        logging.basicConfig(level=logging.DEBUG)


@cli.command()
@click.argument('blueprint_file', type=click.Path(exists=True))
@click.option('--output', '-o', help='Output flow file path')
@click.option('--format', type=click.Choice(['json', 'yaml']), default='json',
              help='Output format')
@click.pass_context
def compile(ctx, blueprint_file: str, output: str, format: str):
    """Compile agent blueprint to AI Applications Flow"""
    
    try:
        blueprint_path = Path(blueprint_file)
        
        # Determine output path
        if output:
            output_path = Path(output)
        else:
            output_path = blueprint_path.with_suffix(f'.flow.{format}')
        
        # Compile blueprint
        click.echo(f"Compiling blueprint: {blueprint_path}")
        flow = compile_blueprint_to_flow(blueprint_path, output_path)
        
        click.echo(f"✓ Flow compiled successfully")
        click.echo(f"  Input: {blueprint_path}")
        click.echo(f"  Output: {output_path}")
        click.echo(f"  Nodes: {len(flow.nodes)}")
        click.echo(f"  Connections: {len(flow.connections)}")
        
        if ctx.obj['verbose']:
            click.echo(f"\nFlow metadata:")
            for key, value in flow.metadata.items():
                click.echo(f"  {key}: {value}")
        
    except Exception as e:
        click.echo(f"Error compiling blueprint: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.argument('flow_file', type=click.Path(exists=True))
@click.option('--input', '-i', help='Input JSON file or JSON string')
@click.option('--project-id', envvar='FOUNDRY_PROJECT_ID', required=True,
              help='Google Cloud Project ID')
@click.option('--execution-id', help='Custom execution ID')
@click.option('--user-id', help='User ID for context')
@click.option('--session-id', help='Session ID for context')
@click.option('--environment', default='development', help='Execution environment')
@click.pass_context
def execute(ctx, flow_file: str, input: str, project_id: str, execution_id: str,
           user_id: str, session_id: str, environment: str):
    """Execute an AI Applications Flow"""
    
    try:
        # Load flow definition
        flow_path = Path(flow_file)
        with open(flow_path, 'r') as f:
            if flow_path.suffix == '.json':
                flow_definition = json.load(f)
            elif flow_path.suffix in ['.yaml', '.yml']:
                flow_definition = yaml.safe_load(f)
            else:
                raise ValueError("Flow file must be JSON or YAML")
        
        # Parse input
        if input:
            if input.startswith('{'):
                # JSON string
                flow_inputs = json.loads(input)
            else:
                # File path
                input_path = Path(input)
                with open(input_path, 'r') as f:
                    if input_path.suffix == '.json':
                        flow_inputs = json.load(f)
                    else:
                        flow_inputs = yaml.safe_load(f)
        else:
            # Default input
            flow_inputs = {
                "user_message": "Hello, this is a test message."
            }
        
        # Create execution context
        context = ExecutionContext(
            execution_id=execution_id or f"cli-{flow_definition['name']}-{int(asyncio.get_event_loop().time())}",
            flow_name=flow_definition['name'],
            flow_version=flow_definition['version'],
            user_id=user_id,
            session_id=session_id,
            environment=environment
        )
        
        click.echo(f"Executing flow: {flow_definition['name']} v{flow_definition['version']}")
        click.echo(f"Execution ID: {context.execution_id}")
        click.echo(f"Environment: {environment}")
        
        if ctx.obj['verbose']:
            click.echo(f"Input: {json.dumps(flow_inputs, indent=2)}")
        
        # Execute flow
        async def run_execution():
            return await execute_flow(flow_definition, flow_inputs, project_id, context)
        
        result = asyncio.run(run_execution())
        
        # Display results
        if result['success']:
            click.echo(f"✓ Flow execution completed successfully")
            click.echo(f"Response: {json.dumps(result['output'], indent=2)}")
        else:
            click.echo(f"✗ Flow execution failed")
            click.echo(f"Error: {result.get('error', 'Unknown error')}")
        
        if ctx.obj['verbose']:
            click.echo(f"\nExecution metrics:")
            metrics = result.get('metrics', {})
            for key, value in metrics.items():
                if key != 'errors':
                    click.echo(f"  {key}: {value}")
            
            if metrics.get('errors'):
                click.echo(f"  Errors:")
                for error in metrics['errors']:
                    click.echo(f"    - {error['node_id']}: {error['error']}")
        
        if not result['success']:
            sys.exit(1)
        
    except Exception as e:
        click.echo(f"Error executing flow: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.argument('blueprint_file', type=click.Path(exists=True))
@click.option('--input', '-i', help='Input JSON file or JSON string')
@click.option('--project-id', envvar='FOUNDRY_PROJECT_ID', required=True,
              help='Google Cloud Project ID')
@click.option('--temp-dir', help='Temporary directory for compiled flow')
@click.pass_context
def run(ctx, blueprint_file: str, input: str, project_id: str, temp_dir: str):
    """Compile and execute blueprint in one step"""
    
    try:
        blueprint_path = Path(blueprint_file)
        
        # Determine temp directory
        if temp_dir:
            temp_path = Path(temp_dir)
        else:
            temp_path = Path.cwd() / "temp"
        
        temp_path.mkdir(exist_ok=True)
        
        # Compile blueprint
        click.echo(f"Compiling blueprint: {blueprint_path}")
        flow_path = temp_path / f"{blueprint_path.stem}.flow.json"
        flow = compile_blueprint_to_flow(blueprint_path, flow_path)
        
        click.echo(f"✓ Blueprint compiled to: {flow_path}")
        
        # Parse input
        if input:
            if input.startswith('{'):
                flow_inputs = json.loads(input)
            else:
                input_path = Path(input)
                with open(input_path, 'r') as f:
                    if input_path.suffix == '.json':
                        flow_inputs = json.load(f)
                    else:
                        flow_inputs = yaml.safe_load(f)
        else:
            flow_inputs = {
                "user_message": "Hello, this is a test message."
            }
        
        # Create execution context
        context = ExecutionContext(
            execution_id=f"run-{flow.name}-{int(asyncio.get_event_loop().time())}",
            flow_name=flow.name,
            flow_version=flow.version,
            environment="development"
        )
        
        click.echo(f"Executing flow: {flow.name}")
        
        # Execute flow
        async def run_execution():
            return await execute_flow(flow.to_dict(), flow_inputs, project_id, context)
        
        result = asyncio.run(run_execution())
        
        # Display results
        if result['success']:
            click.echo(f"✓ Execution completed successfully")
            click.echo(f"Response: {json.dumps(result['output'], indent=2)}")
        else:
            click.echo(f"✗ Execution failed")
            click.echo(f"Error: {result.get('error', 'Unknown error')}")
        
        # Cleanup temp file unless verbose
        if not ctx.obj['verbose']:
            flow_path.unlink(missing_ok=True)
        else:
            click.echo(f"Flow file preserved: {flow_path}")
        
        if not result['success']:
            sys.exit(1)
        
    except Exception as e:
        click.echo(f"Error in run command: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.argument('flow_file', type=click.Path(exists=True))
@click.pass_context
def validate(ctx, flow_file: str):
    """Validate a compiled flow definition"""
    
    try:
        flow_path = Path(flow_file)
        
        # Load flow
        with open(flow_path, 'r') as f:
            if flow_path.suffix == '.json':
                flow_definition = json.load(f)
            elif flow_path.suffix in ['.yaml', '.yml']:
                flow_definition = yaml.safe_load(f)
            else:
                raise ValueError("Flow file must be JSON or YAML")
        
        # Basic validation
        required_fields = ['name', 'version', 'nodes', 'connections']
        missing_fields = [field for field in required_fields if field not in flow_definition]
        
        if missing_fields:
            click.echo(f"✗ Flow validation failed")
            click.echo(f"Missing required fields: {', '.join(missing_fields)}")
            sys.exit(1)
        
        # Validate nodes
        nodes = flow_definition['nodes']
        node_ids = {node['id'] for node in nodes}
        
        # Validate connections
        connections = flow_definition['connections']
        for conn in connections:
            if conn['source_node'] not in node_ids:
                click.echo(f"✗ Invalid connection: source node '{conn['source_node']}' not found")
                sys.exit(1)
            if conn['target_node'] not in node_ids:
                click.echo(f"✗ Invalid connection: target node '{conn['target_node']}' not found")
                sys.exit(1)
        
        click.echo(f"✓ Flow validation passed")
        click.echo(f"  Name: {flow_definition['name']}")
        click.echo(f"  Version: {flow_definition['version']}")
        click.echo(f"  Nodes: {len(nodes)}")
        click.echo(f"  Connections: {len(connections)}")
        
        if ctx.obj['verbose']:
            click.echo(f"\nNode types:")
            node_types = {}
            for node in nodes:
                node_type = node.get('type', 'unknown')
                node_types[node_type] = node_types.get(node_type, 0) + 1
            
            for node_type, count in node_types.items():
                click.echo(f"  {node_type}: {count}")
        
    except Exception as e:
        click.echo(f"Error validating flow: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.argument('flow_file', type=click.Path(exists=True))
@click.option('--output', '-o', help='Output visualization file (HTML)')
@click.pass_context
def visualize(ctx, flow_file: str, output: str):
    """Generate flow visualization"""
    
    try:
        flow_path = Path(flow_file)
        
        # Load flow
        with open(flow_path, 'r') as f:
            if flow_path.suffix == '.json':
                flow_definition = json.load(f)
            elif flow_path.suffix in ['.yaml', '.yml']:
                flow_definition = yaml.safe_load(f)
            else:
                raise ValueError("Flow file must be JSON or YAML")
        
        # Generate simple text visualization
        click.echo(f"Flow: {flow_definition['name']} v{flow_definition['version']}")
        click.echo("=" * 50)
        
        nodes = {node['id']: node for node in flow_definition['nodes']}
        connections = flow_definition['connections']
        
        # Build adjacency list
        graph = {}
        for node_id in nodes:
            graph[node_id] = []
        
        for conn in connections:
            graph[conn['source_node']].append(conn['target_node'])
        
        # Simple topological display
        click.echo("\nFlow Structure:")
        for node_id, node in nodes.items():
            node_type = node.get('type', 'unknown')
            targets = graph.get(node_id, [])
            
            click.echo(f"  {node_id} ({node_type})")
            if targets:
                for target in targets:
                    target_type = nodes[target].get('type', 'unknown')
                    click.echo(f"    └─> {target} ({target_type})")
        
        # Generate HTML visualization if requested
        if output:
            html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>Flow Visualization: {flow_definition['name']}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .node {{ 
            border: 1px solid #ccc; 
            border-radius: 5px; 
            padding: 10px; 
            margin: 10px; 
            background: #f9f9f9; 
        }}
        .connection {{ color: #666; margin-left: 20px; }}
    </style>
</head>
<body>
    <h1>Flow: {flow_definition['name']} v{flow_definition['version']}</h1>
    <div class="flow">
"""
            
            for node_id, node in nodes.items():
                node_type = node.get('type', 'unknown')
                html_content += f'        <div class="node"><strong>{node_id}</strong> ({node_type})</div>\n'
                
                targets = graph.get(node_id, [])
                for target in targets:
                    target_type = nodes[target].get('type', 'unknown')
                    html_content += f'        <div class="connection">└─> {target} ({target_type})</div>\n'
            
            html_content += """
    </div>
</body>
</html>
"""
            
            output_path = Path(output)
            with open(output_path, 'w') as f:
                f.write(html_content)
            
            click.echo(f"\n✓ HTML visualization saved to: {output_path}")
        
    except Exception as e:
        click.echo(f"Error generating visualization: {e}", err=True)
        sys.exit(1)


if __name__ == '__main__':
    cli()
