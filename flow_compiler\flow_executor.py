"""
AI Applications Flow Execution Engine
Executes compiled flows with performance monitoring and error handling
"""

import asyncio
import json
import time
import uuid
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass
from datetime import datetime, timezone
import logging

from google.cloud import pubsub_v1, bigquery, firestore
from google.auth import default

logger = logging.getLogger(__name__)


@dataclass
class ExecutionContext:
    """Context for flow execution"""
    execution_id: str
    flow_name: str
    flow_version: str
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    environment: str = "development"
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class NodeResult:
    """Result from node execution"""
    node_id: str
    success: bool
    outputs: Dict[str, Any]
    execution_time_ms: float
    error: Optional[str] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class FlowExecutor:
    """Executes AI Applications Flows"""
    
    def __init__(self, project_id: str):
        self.project_id = project_id
        self.credentials, _ = default()
        
        # Initialize Google Cloud clients
        self.publisher = pubsub_v1.PublisherClient(credentials=self.credentials)
        self.bigquery_client = bigquery.Client(project=project_id, credentials=self.credentials)
        self.firestore_client = firestore.Client(project=project_id, credentials=self.credentials)
        
        # Node executors registry
        self.node_executors = {
            "input_processor": self._execute_input_processor,
            "model_armor": self._execute_model_armor,
            "memory_retrieval": self._execute_memory_retrieval,
            "llm_processor": self._execute_llm_processor,
            "tool_executor": self._execute_tool_executor,
            "response_processor": self._execute_response_processor,
            "response_evaluator": self._execute_response_evaluator,
            "output_formatter": self._execute_output_formatter,
            "observability_collector": self._execute_observability_collector
        }
        
        # Execution state
        self.node_results = {}
        self.execution_metrics = {}
    
    async def execute_flow(self, flow: Dict[str, Any], inputs: Dict[str, Any], 
                          context: ExecutionContext) -> Dict[str, Any]:
        """Execute a complete flow"""
        
        logger.info(f"Starting flow execution: {context.execution_id}")
        start_time = time.time()
        
        try:
            # Initialize execution state
            self.node_results = {}
            self.execution_metrics = {
                "execution_id": context.execution_id,
                "flow_name": flow["name"],
                "flow_version": flow["version"],
                "started_at": datetime.now(timezone.utc).isoformat(),
                "nodes_executed": 0,
                "total_nodes": len(flow["nodes"]),
                "errors": []
            }
            
            # Build execution graph
            nodes_by_id = {node["id"]: node for node in flow["nodes"]}
            connections_by_target = {}
            
            for conn in flow["connections"]:
                target = conn["target_node"]
                if target not in connections_by_target:
                    connections_by_target[target] = []
                connections_by_target[target].append(conn)
            
            # Find entry nodes (nodes with no inputs)
            entry_nodes = []
            for node in flow["nodes"]:
                if node["id"] not in connections_by_target:
                    entry_nodes.append(node)
            
            # Execute nodes in topological order
            executed_nodes = set()
            pending_nodes = entry_nodes.copy()
            
            while pending_nodes:
                # Find nodes ready to execute (all dependencies satisfied)
                ready_nodes = []
                for node in pending_nodes:
                    if self._are_dependencies_satisfied(node["id"], connections_by_target, executed_nodes):
                        ready_nodes.append(node)
                
                if not ready_nodes:
                    raise RuntimeError("Circular dependency detected in flow")
                
                # Execute ready nodes in parallel
                tasks = []
                for node in ready_nodes:
                    task = self._execute_node(node, connections_by_target, inputs, context)
                    tasks.append(task)
                
                # Wait for all nodes to complete
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # Process results
                for i, result in enumerate(results):
                    node = ready_nodes[i]
                    if isinstance(result, Exception):
                        logger.error(f"Node {node['id']} failed: {result}")
                        self.execution_metrics["errors"].append({
                            "node_id": node["id"],
                            "error": str(result),
                            "timestamp": datetime.now(timezone.utc).isoformat()
                        })
                        # Create error result
                        self.node_results[node["id"]] = NodeResult(
                            node_id=node["id"],
                            success=False,
                            outputs={},
                            execution_time_ms=0,
                            error=str(result)
                        )
                    else:
                        self.node_results[node["id"]] = result
                    
                    executed_nodes.add(node["id"])
                    self.execution_metrics["nodes_executed"] += 1
                
                # Remove executed nodes from pending
                pending_nodes = [n for n in pending_nodes if n not in ready_nodes]
                
                # Add newly ready nodes
                for node in flow["nodes"]:
                    if (node["id"] not in executed_nodes and 
                        node not in pending_nodes and
                        self._are_dependencies_satisfied(node["id"], connections_by_target, executed_nodes)):
                        pending_nodes.append(node)
            
            # Calculate execution metrics
            execution_time = (time.time() - start_time) * 1000
            self.execution_metrics.update({
                "completed_at": datetime.now(timezone.utc).isoformat(),
                "total_execution_time_ms": execution_time,
                "success": len(self.execution_metrics["errors"]) == 0
            })
            
            # Find output node result
            output_nodes = [n for n in flow["nodes"] if n["type"] == "output_formatter"]
            if output_nodes:
                output_result = self.node_results.get(output_nodes[0]["id"])
                if output_result and output_result.success:
                    final_output = output_result.outputs.get("formatted_response", {})
                else:
                    final_output = {"error": "Output node failed"}
            else:
                final_output = {"error": "No output node found"}
            
            # Log execution completion
            await self._log_execution_metrics(context)
            
            logger.info(f"Flow execution completed: {context.execution_id}")
            return {
                "execution_id": context.execution_id,
                "success": self.execution_metrics["success"],
                "output": final_output,
                "metrics": self.execution_metrics
            }
            
        except Exception as e:
            logger.error(f"Flow execution failed: {e}")
            self.execution_metrics.update({
                "completed_at": datetime.now(timezone.utc).isoformat(),
                "total_execution_time_ms": (time.time() - start_time) * 1000,
                "success": False,
                "fatal_error": str(e)
            })
            
            await self._log_execution_metrics(context)
            
            return {
                "execution_id": context.execution_id,
                "success": False,
                "error": str(e),
                "metrics": self.execution_metrics
            }
    
    async def _execute_node(self, node: Dict[str, Any], connections_by_target: Dict[str, List],
                           flow_inputs: Dict[str, Any], context: ExecutionContext) -> NodeResult:
        """Execute a single node"""
        
        node_id = node["id"]
        node_type = node["type"]
        
        logger.debug(f"Executing node: {node_id} ({node_type})")
        start_time = time.time()
        
        try:
            # Gather inputs for this node
            node_inputs = {}
            
            # Add flow inputs for entry nodes
            if node_id not in connections_by_target:
                node_inputs.update(flow_inputs)
            
            # Add inputs from connected nodes
            if node_id in connections_by_target:
                for conn in connections_by_target[node_id]:
                    source_result = self.node_results.get(conn["source_node"])
                    if source_result and source_result.success:
                        source_output = source_result.outputs.get(conn["source_output"])
                        if source_output is not None:
                            node_inputs[conn["target_input"]] = source_output
            
            # Execute node
            executor = self.node_executors.get(node_type)
            if not executor:
                raise ValueError(f"Unknown node type: {node_type}")
            
            outputs = await executor(node, node_inputs, context)
            
            execution_time = (time.time() - start_time) * 1000
            
            return NodeResult(
                node_id=node_id,
                success=True,
                outputs=outputs,
                execution_time_ms=execution_time,
                metadata={"node_type": node_type}
            )
            
        except Exception as e:
            execution_time = (time.time() - start_time) * 1000
            logger.error(f"Node {node_id} execution failed: {e}")
            
            return NodeResult(
                node_id=node_id,
                success=False,
                outputs={},
                execution_time_ms=execution_time,
                error=str(e),
                metadata={"node_type": node_type}
            )
    
    def _are_dependencies_satisfied(self, node_id: str, connections_by_target: Dict[str, List],
                                   executed_nodes: set) -> bool:
        """Check if all dependencies for a node are satisfied"""
        if node_id not in connections_by_target:
            return True  # No dependencies
        
        for conn in connections_by_target[node_id]:
            if conn["source_node"] not in executed_nodes:
                return False
        
        return True
    
    # Node executor implementations
    
    async def _execute_input_processor(self, node: Dict[str, Any], inputs: Dict[str, Any],
                                     context: ExecutionContext) -> Dict[str, Any]:
        """Execute input processor node"""
        config = node["config"]
        user_message = inputs.get("user_message", "")
        
        # Input validation
        if config.get("input_validation", True):
            if not user_message.strip():
                raise ValueError("Empty user message")
            
            max_length = config.get("preprocessing", {}).get("max_length", 10000)
            if len(user_message) > max_length:
                raise ValueError(f"Message too long: {len(user_message)} > {max_length}")
        
        # Preprocessing
        if config.get("preprocessing", {}).get("trim_whitespace", True):
            user_message = user_message.strip()
        
        return {
            "user_message": user_message,
            "metadata": {
                "original_length": len(inputs.get("user_message", "")),
                "processed_length": len(user_message),
                "timestamp": datetime.now(timezone.utc).isoformat()
            },
            "metrics": {
                "input_length": len(user_message),
                "preprocessing_applied": True
            }
        }
    
    async def _execute_model_armor(self, node: Dict[str, Any], inputs: Dict[str, Any],
                                 context: ExecutionContext) -> Dict[str, Any]:
        """Execute Model Armor security node"""
        config = node["config"]
        content = inputs.get("content", "")
        
        # Simulate Model Armor processing
        # In real implementation, this would call the actual Model Armor API
        
        # Simple content filtering simulation
        blocked_terms = ["hack", "exploit", "malware", "virus"]
        violation_detected = any(term in content.lower() for term in blocked_terms)
        
        if violation_detected and config.get("block_on_violation", True):
            raise ValueError("Content blocked by Model Armor policy")
        
        return {
            "approved_content": content,
            "violation_details": {
                "violation_detected": violation_detected,
                "policy": config.get("policy", "PII_LOW"),
                "action": "BLOCK" if violation_detected else "ALLOW"
            },
            "metrics": {
                "content_length": len(content),
                "policy_applied": config.get("policy", "PII_LOW"),
                "violation_detected": violation_detected
            }
        }
    
    async def _execute_memory_retrieval(self, node: Dict[str, Any], inputs: Dict[str, Any],
                                      context: ExecutionContext) -> Dict[str, Any]:
        """Execute memory retrieval node"""
        config = node["config"]
        query = inputs.get("query", "")
        
        # Simulate memory retrieval
        # In real implementation, this would query the vector database
        
        retrieved_memories = [
            {
                "content": "Previous conversation about similar topic",
                "similarity": 0.85,
                "timestamp": "2024-01-01T10:00:00Z"
            }
        ]
        
        context_text = "\n".join([mem["content"] for mem in retrieved_memories])
        
        return {
            "context": context_text,
            "retrieved_memories": retrieved_memories,
            "metrics": {
                "query_length": len(query),
                "memories_retrieved": len(retrieved_memories),
                "avg_similarity": 0.85
            }
        }
    
    async def _execute_llm_processor(self, node: Dict[str, Any], inputs: Dict[str, Any],
                                   context: ExecutionContext) -> Dict[str, Any]:
        """Execute LLM processor node"""
        config = node["config"]
        user_input = inputs.get("user_input", "")
        context_text = inputs.get("context", "")
        
        # Simulate LLM processing
        # In real implementation, this would call the actual LLM API
        
        model = config["model"]
        instructions = config["instructions"]
        
        # Simulate response generation
        response = f"This is a simulated response from {model} for: {user_input}"
        
        # Simulate token usage
        input_tokens = len(user_input.split()) + len(context_text.split())
        output_tokens = len(response.split())
        
        return {
            "response": response,
            "tool_calls": [],  # Simplified - no tool calls in this simulation
            "usage_metadata": {
                "input_tokens": input_tokens,
                "output_tokens": output_tokens,
                "total_tokens": input_tokens + output_tokens
            },
            "metrics": {
                "model": model,
                "input_tokens": input_tokens,
                "output_tokens": output_tokens,
                "processing_time_ms": 250  # Simulated
            }
        }
    
    async def _execute_tool_executor(self, node: Dict[str, Any], inputs: Dict[str, Any],
                                   context: ExecutionContext) -> Dict[str, Any]:
        """Execute tool executor node"""
        config = node["config"]
        function_call = inputs.get("function_call", {})
        
        # Simulate tool execution
        tool_type = config["tool_type"]
        
        result = {
            "status": "success",
            "data": f"Simulated result from {tool_type}",
            "execution_time_ms": 100
        }
        
        return {
            "result": result,
            "error": None,
            "metrics": {
                "tool_type": tool_type,
                "execution_time_ms": 100,
                "success": True
            }
        }
    
    async def _execute_response_processor(self, node: Dict[str, Any], inputs: Dict[str, Any],
                                        context: ExecutionContext) -> Dict[str, Any]:
        """Execute response processor node"""
        config = node["config"]
        llm_response = inputs.get("llm_response", "")
        tool_results = inputs.get("tool_results", [])
        
        # Process and format response
        final_response = llm_response
        
        if tool_results and config.get("include_citations", False):
            final_response += "\n\nBased on tool results: " + str(tool_results)
        
        return {
            "final_response": final_response,
            "response_metadata": {
                "length": len(final_response),
                "tool_results_included": bool(tool_results),
                "formatted_at": datetime.now(timezone.utc).isoformat()
            },
            "metrics": {
                "response_length": len(final_response),
                "tools_used": len(tool_results)
            }
        }
    
    async def _execute_response_evaluator(self, node: Dict[str, Any], inputs: Dict[str, Any],
                                        context: ExecutionContext) -> Dict[str, Any]:
        """Execute response evaluator node"""
        config = node["config"]
        response = inputs.get("response", "")
        
        # Simulate evaluation
        evaluation_scores = {
            "hallucination_rate": 0.01,  # Well within SLA
            "toxicity_score": 0.02,      # Well within SLA
            "quality_score": 0.85,
            "relevance_score": 0.90
        }
        
        # Check if response meets quality thresholds
        hallucination_threshold = config.get("hallucination_detector", {}).get("threshold", 0.02)
        toxicity_threshold = config.get("toxicity_detector", {}).get("threshold", 0.05)
        
        if evaluation_scores["hallucination_rate"] > hallucination_threshold:
            raise ValueError(f"Hallucination rate too high: {evaluation_scores['hallucination_rate']}")
        
        if evaluation_scores["toxicity_score"] > toxicity_threshold:
            raise ValueError(f"Toxicity score too high: {evaluation_scores['toxicity_score']}")
        
        return {
            "validated_response": response,
            "evaluation_scores": evaluation_scores,
            "metrics": {
                "evaluation_passed": True,
                "scores": evaluation_scores
            }
        }
    
    async def _execute_output_formatter(self, node: Dict[str, Any], inputs: Dict[str, Any],
                                      context: ExecutionContext) -> Dict[str, Any]:
        """Execute output formatter node"""
        config = node["config"]
        response = inputs.get("response", "")
        metadata = inputs.get("metadata", {})
        
        formatted_response = {
            "response": response,
            "execution_id": context.execution_id,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
        if config.get("include_metadata", True):
            formatted_response["metadata"] = metadata
        
        return {
            "formatted_response": formatted_response,
            "metrics": {
                "format": config.get("format", "json"),
                "response_size": len(json.dumps(formatted_response))
            }
        }
    
    async def _execute_observability_collector(self, node: Dict[str, Any], inputs: Dict[str, Any],
                                             context: ExecutionContext) -> Dict[str, Any]:
        """Execute observability collector node"""
        config = node["config"]
        metrics_data = inputs.get("metrics_data", {})
        
        # Collect and aggregate metrics
        aggregated_metrics = {
            "agent_name": config["agent_name"],
            "agent_version": config["agent_version"],
            "model": config["model"],
            "execution_id": context.execution_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "metrics": metrics_data
        }
        
        # In real implementation, this would publish to Pub/Sub
        logger.debug(f"Observability data collected: {aggregated_metrics}")
        
        return {
            "observability_status": "collected",
            "metrics": {
                "data_points_collected": len(metrics_data),
                "collection_successful": True
            }
        }

    async def _log_execution_metrics(self, context: ExecutionContext) -> None:
        """Log execution metrics to observability system"""
        try:
            # In real implementation, this would publish to Pub/Sub for BigQuery ingestion
            logger.info(f"Execution metrics: {json.dumps(self.execution_metrics, indent=2)}")
        except Exception as e:
            logger.error(f"Failed to log execution metrics: {e}")


# Convenience function for flow execution
async def execute_flow(flow_definition: Dict[str, Any], inputs: Dict[str, Any],
                      project_id: str, execution_context: ExecutionContext = None) -> Dict[str, Any]:
    """Execute a flow with given inputs"""

    if execution_context is None:
        execution_context = ExecutionContext(
            execution_id=str(uuid.uuid4()),
            flow_name=flow_definition.get("name", "unknown"),
            flow_version=flow_definition.get("version", "1.0.0")
        )

    executor = FlowExecutor(project_id)
    return await executor.execute_flow(flow_definition, inputs, execution_context)
