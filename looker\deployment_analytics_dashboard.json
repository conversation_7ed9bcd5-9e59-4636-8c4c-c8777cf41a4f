{"displayName": "Metamorphic Foundry - Deployment Analytics", "mosaicLayout": {"tiles": [{"width": 12, "height": 4, "widget": {"title": "Deployment Success Rate by Environment", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"bigquery_dataset\"", "aggregation": {"alignmentPeriod": "3600s", "perSeriesAligner": "ALIGN_MEAN", "crossSeriesReducer": "REDUCE_MEAN", "groupByFields": ["metric.label.environment"]}}}, "plotType": "LINE", "targetAxis": "Y1", "legendTemplate": "${metric.label.environment}"}], "timeshiftDuration": "0s", "yAxis": {"label": "Success Rate (%)", "scale": "LINEAR"}, "chartOptions": {"mode": "COLOR"}, "thresholds": [{"value": 95, "color": "GREEN", "direction": "ABOVE", "label": "Target Success Rate"}, {"value": 80, "color": "YELLOW", "direction": "ABOVE", "label": "Warning Threshold"}]}}}, {"yPos": 4, "width": 6, "height": 4, "widget": {"title": "Deployment Frequency", "scorecard": {"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"bigquery_dataset\"", "aggregation": {"alignmentPeriod": "86400s", "perSeriesAligner": "ALIGN_SUM", "crossSeriesReducer": "REDUCE_SUM"}}, "unitOverride": "deployments/day"}, "sparkChartView": {"sparkChartType": "SPARK_BAR"}, "gaugeView": {"lowerBound": 0, "upperBound": 50}}}}, {"xPos": 6, "yPos": 4, "width": 6, "height": 4, "widget": {"title": "Canary Pass Rate", "scorecard": {"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"bigquery_dataset\"", "aggregation": {"alignmentPeriod": "3600s", "perSeriesAligner": "ALIGN_MEAN", "crossSeriesReducer": "REDUCE_MEAN"}}, "unitOverride": "%"}, "sparkChartView": {"sparkChartType": "SPARK_LINE"}, "thresholds": [{"value": 95, "color": "GREEN", "direction": "ABOVE"}, {"value": 85, "color": "YELLOW", "direction": "ABOVE"}]}}}, {"yPos": 8, "width": 12, "height": 6, "widget": {"title": "Recent Deployments Status", "table": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"bigquery_dataset\"", "aggregation": {"alignmentPeriod": "3600s", "perSeriesAligner": "ALIGN_MAX", "crossSeriesReducer": "REDUCE_MAX", "groupByFields": ["metric.label.agent_name", "metric.label.deployment_status", "metric.label.git_commit"]}}}, "tableTemplate": "${metric.label.agent_name} (${metric.label.git_commit})", "tableDisplayOptions": {"shownColumns": ["METRIC_LABELS", "VALUE", "TIME_SERIES"]}}], "columnSettings": [{"column": "VALUE", "displayName": "Status"}, {"column": "METRIC_LABELS", "displayName": "Agent (Commit)"}]}}}, {"yPos": 14, "width": 6, "height": 4, "widget": {"title": "Rollback Rate", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"bigquery_dataset\"", "aggregation": {"alignmentPeriod": "86400s", "perSeriesAligner": "ALIGN_MEAN", "crossSeriesReducer": "REDUCE_MEAN", "groupByFields": ["metric.label.environment"]}}}, "plotType": "STACKED_BAR", "targetAxis": "Y1", "legendTemplate": "${metric.label.environment}"}], "timeshiftDuration": "0s", "yAxis": {"label": "Rollback Rate (%)", "scale": "LINEAR"}, "chartOptions": {"mode": "COLOR"}, "thresholds": [{"value": 5, "color": "YELLOW", "direction": "ABOVE", "label": "Warning Threshold"}, {"value": 10, "color": "RED", "direction": "ABOVE", "label": "Critical Threshold"}]}}}, {"xPos": 6, "yPos": 14, "width": 6, "height": 4, "widget": {"title": "Time to Deploy (Minutes)", "scorecard": {"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"bigquery_dataset\"", "aggregation": {"alignmentPeriod": "3600s", "perSeriesAligner": "ALIGN_MEAN", "crossSeriesReducer": "REDUCE_MEAN"}}, "unitOverride": "min"}, "sparkChartView": {"sparkChartType": "SPARK_LINE"}, "thresholds": [{"value": 30, "color": "GREEN", "direction": "BELOW"}, {"value": 60, "color": "YELLOW", "direction": "BELOW"}]}}}, {"yPos": 18, "width": 12, "height": 4, "widget": {"title": "A2A Manifest Tracking", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"bigquery_dataset\"", "aggregation": {"alignmentPeriod": "86400s", "perSeriesAligner": "ALIGN_SUM", "crossSeriesReducer": "REDUCE_SUM", "groupByFields": ["metric.label.a2a_manifest_status"]}}}, "plotType": "STACKED_AREA", "targetAxis": "Y1", "legendTemplate": "${metric.label.a2a_manifest_status}"}], "timeshiftDuration": "0s", "yAxis": {"label": "Deployments with A2A", "scale": "LINEAR"}, "chartOptions": {"mode": "COLOR"}}}}, {"yPos": 22, "width": 6, "height": 4, "widget": {"title": "Unique Deployers", "scorecard": {"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"bigquery_dataset\"", "aggregation": {"alignmentPeriod": "86400s", "perSeriesAligner": "ALIGN_COUNT", "crossSeriesReducer": "REDUCE_COUNT"}}, "unitOverride": "users"}, "sparkChartView": {"sparkChartType": "SPARK_BAR"}, "gaugeView": {"lowerBound": 0, "upperBound": 20}}}}, {"xPos": 6, "yPos": 22, "width": 6, "height": 4, "widget": {"title": "Git Commits Deployed", "scorecard": {"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"bigquery_dataset\"", "aggregation": {"alignmentPeriod": "86400s", "perSeriesAligner": "ALIGN_COUNT", "crossSeriesReducer": "REDUCE_COUNT"}}, "unitOverride": "commits"}, "sparkChartView": {"sparkChartType": "SPARK_LINE"}, "gaugeView": {"lowerBound": 0, "upperBound": 100}}}}, {"yPos": 26, "width": 12, "height": 4, "widget": {"title": "Deployment Pipeline Health", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"bigquery_dataset\"", "aggregation": {"alignmentPeriod": "3600s", "perSeriesAligner": "ALIGN_MEAN", "crossSeriesReducer": "REDUCE_MEAN", "groupByFields": ["metric.label.pipeline_stage"]}}}, "plotType": "LINE", "targetAxis": "Y1", "legendTemplate": "${metric.label.pipeline_stage}"}], "timeshiftDuration": "0s", "yAxis": {"label": "Success Rate (%)", "scale": "LINEAR"}, "chartOptions": {"mode": "COLOR"}}}}, {"yPos": 30, "width": 12, "height": 6, "widget": {"title": "Deployment Operations Guide", "text": {"content": "## Deployment Analytics Dashboard\n\n### Key Metrics\n- **Success Rate**: Target ≥ 95% for production deployments\n- **Canary Pass Rate**: Target ≥ 95% before production promotion\n- **Time to Deploy**: Target < 30 minutes (time-to-first-agent KPI)\n- **Rollback Rate**: Target < 5% of total deployments\n\n### Pipeline Stages\n1. **Lint & Unit Tests**: Code quality validation\n2. **Security Gates**: Model Armor and OPA policy validation\n3. **Canary Deployment**: Staging environment testing\n4. **Production Deployment**: Blue-green deployment with A2A manifest\n\n### Quick Actions\n- [View Pipeline Logs](https://console.cloud.google.com/cloud-build)\n- [Deployment History](#)\n- [Rollback Procedures](#)\n- [A2A Manifest Validator](#)", "format": "MARKDOWN"}}}]}, "labels": {"team": "ai-foundry", "environment": "production", "dashboard_type": "deployment"}}