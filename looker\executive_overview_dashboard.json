{"displayName": "Metamorphic Foundry - Executive Overview", "mosaicLayout": {"tiles": [{"width": 6, "height": 4, "widget": {"title": "Daily Agent Turns", "scorecard": {"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"bigquery_dataset\"", "aggregation": {"alignmentPeriod": "86400s", "perSeriesAligner": "ALIGN_SUM", "crossSeriesReducer": "REDUCE_SUM"}}, "unitOverride": "1"}, "sparkChartView": {"sparkChartType": "SPARK_LINE"}, "thresholds": [{"value": 10000, "color": "GREEN", "direction": "ABOVE"}, {"value": 5000, "color": "YELLOW", "direction": "ABOVE"}]}}}, {"xPos": 6, "width": 6, "height": 4, "widget": {"title": "Active Agents", "scorecard": {"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"bigquery_dataset\"", "aggregation": {"alignmentPeriod": "86400s", "perSeriesAligner": "ALIGN_MEAN", "crossSeriesReducer": "REDUCE_COUNT"}}, "unitOverride": "1"}, "sparkChartView": {"sparkChartType": "SPARK_BAR"}, "thresholds": [{"value": 50, "color": "GREEN", "direction": "ABOVE"}, {"value": 20, "color": "YELLOW", "direction": "ABOVE"}]}}}, {"yPos": 4, "width": 12, "height": 6, "widget": {"title": "KPI Status Dashboard", "table": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"bigquery_dataset\"", "aggregation": {"alignmentPeriod": "86400s", "perSeriesAligner": "ALIGN_MEAN", "crossSeriesReducer": "REDUCE_MEAN", "groupByFields": ["metric.label.kpi_name"]}}}, "tableTemplate": "${metric.label.kpi_name}", "tableDisplayOptions": {"shownColumns": ["METRIC_LABELS", "VALUE", "TIME_SERIES"]}}], "columnSettings": [{"column": "VALUE", "displayName": "Current Value"}, {"column": "METRIC_LABELS", "displayName": "KPI"}]}}}, {"yPos": 10, "width": 6, "height": 4, "widget": {"title": "Hallucination Rate Trend", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"bigquery_dataset\"", "aggregation": {"alignmentPeriod": "3600s", "perSeriesAligner": "ALIGN_MEAN", "crossSeriesReducer": "REDUCE_MEAN"}}}, "plotType": "LINE", "targetAxis": "Y1", "legendTemplate": "Hallucination Rate"}], "timeshiftDuration": "0s", "yAxis": {"label": "Rate (%)", "scale": "LINEAR"}, "chartOptions": {"mode": "COLOR"}, "thresholds": [{"value": 2.0, "color": "RED", "direction": "ABOVE", "label": "SLA Threshold"}]}}}, {"xPos": 6, "yPos": 10, "width": 6, "height": 4, "widget": {"title": "Toxicity Score Trend", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"bigquery_dataset\"", "aggregation": {"alignmentPeriod": "3600s", "perSeriesAligner": "ALIGN_MEAN", "crossSeriesReducer": "REDUCE_MEAN"}}}, "plotType": "LINE", "targetAxis": "Y1", "legendTemplate": "Toxicity Score"}], "timeshiftDuration": "0s", "yAxis": {"label": "Score", "scale": "LINEAR"}, "chartOptions": {"mode": "COLOR"}, "thresholds": [{"value": 0.05, "color": "RED", "direction": "ABOVE", "label": "SLA Threshold"}]}}}, {"yPos": 14, "width": 6, "height": 4, "widget": {"title": "Model Usage Distribution", "pieChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"bigquery_dataset\"", "aggregation": {"alignmentPeriod": "86400s", "perSeriesAligner": "ALIGN_SUM", "crossSeriesReducer": "REDUCE_SUM", "groupByFields": ["metric.label.model"]}}}, "legendTemplate": "${metric.label.model}"}], "chartType": "DONUT", "showLabels": true}}}, {"xPos": 6, "yPos": 14, "width": 6, "height": 4, "widget": {"title": "Cost Efficiency Metrics", "scorecard": {"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"bigquery_dataset\"", "aggregation": {"alignmentPeriod": "86400s", "perSeriesAligner": "ALIGN_SUM", "crossSeriesReducer": "REDUCE_SUM"}}, "unitOverride": "$"}, "sparkChartView": {"sparkChartType": "SPARK_LINE"}, "gaugeView": {"lowerBound": 0, "upperBound": 1000}}}}, {"yPos": 18, "width": 12, "height": 4, "widget": {"title": "Deployment Success Rate", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"bigquery_dataset\"", "aggregation": {"alignmentPeriod": "86400s", "perSeriesAligner": "ALIGN_MEAN", "crossSeriesReducer": "REDUCE_MEAN", "groupByFields": ["metric.label.environment"]}}}, "plotType": "STACKED_BAR", "targetAxis": "Y1", "legendTemplate": "${metric.label.environment}"}], "timeshiftDuration": "0s", "yAxis": {"label": "Success Rate (%)", "scale": "LINEAR"}, "chartOptions": {"mode": "COLOR"}}}}, {"yPos": 22, "width": 12, "height": 6, "widget": {"title": "Executive Summary - Key Insights", "text": {"content": "## Metamorphic Foundry Executive Dashboard\n\n### Key Performance Indicators\n- **Time-to-first-agent**: Target < 30 minutes\n- **Model efficiency**: Target ≥ 40% Flash/Gemma usage\n- **Quality gates**: Hallucination ≤ 2%, Toxicity ≤ 0.05\n- **Latency targets**: Flash < 1s, Pro < 8s P95\n\n### Current Status\nThis dashboard provides real-time visibility into agent performance, cost efficiency, and compliance metrics across the Metamorphic Foundry platform.\n\n### Quick Actions\n- [View Detailed Analytics](https://console.cloud.google.com/bigquery)\n- [Agent Performance Deep Dive](#)\n- [Policy Compliance Report](#)\n- [Cost Optimization Recommendations](#)", "format": "MARKDOWN"}}}]}, "labels": {"team": "ai-foundry", "environment": "production", "dashboard_type": "executive"}}