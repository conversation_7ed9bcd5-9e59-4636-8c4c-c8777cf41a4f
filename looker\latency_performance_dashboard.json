{"displayName": "Metamorphic Foundry - Latency & Performance", "mosaicLayout": {"tiles": [{"width": 12, "height": 4, "widget": {"title": "P95 Latency by Model (SLA Compliance)", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"bigquery_dataset\" AND metric.label.model=\"gemini-2.5-flash\"", "aggregation": {"alignmentPeriod": "300s", "perSeriesAligner": "ALIGN_PERCENTILE_95", "crossSeriesReducer": "REDUCE_PERCENTILE_95", "groupByFields": ["metric.label.model", "metric.label.environment"]}}}, "plotType": "LINE", "targetAxis": "Y1", "legendTemplate": "Flash - ${metric.label.environment}"}, {"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"bigquery_dataset\" AND metric.label.model=\"gemini-2.5-pro\"", "aggregation": {"alignmentPeriod": "300s", "perSeriesAligner": "ALIGN_PERCENTILE_95", "crossSeriesReducer": "REDUCE_PERCENTILE_95", "groupByFields": ["metric.label.model", "metric.label.environment"]}}}, "plotType": "LINE", "targetAxis": "Y1", "legendTemplate": "Pro - ${metric.label.environment}"}, {"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"bigquery_dataset\" AND metric.label.model=\"gemma-3n\"", "aggregation": {"alignmentPeriod": "300s", "perSeriesAligner": "ALIGN_PERCENTILE_95", "crossSeriesReducer": "REDUCE_PERCENTILE_95", "groupByFields": ["metric.label.model", "metric.label.environment"]}}}, "plotType": "LINE", "targetAxis": "Y1", "legendTemplate": "Gemma - ${metric.label.environment}"}], "timeshiftDuration": "0s", "yAxis": {"label": "Latency (ms)", "scale": "LINEAR"}, "chartOptions": {"mode": "COLOR"}, "thresholds": [{"value": 1000, "color": "RED", "direction": "ABOVE", "label": "Flash SLA (1s)"}, {"value": 8000, "color": "YELLOW", "direction": "ABOVE", "label": "Pro SLA (8s)"}]}}}, {"yPos": 4, "width": 6, "height": 4, "widget": {"title": "Request Volume by Agent", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"bigquery_dataset\"", "aggregation": {"alignmentPeriod": "300s", "perSeriesAligner": "ALIGN_RATE", "crossSeriesReducer": "REDUCE_SUM", "groupByFields": ["metric.label.agent_name"]}}}, "plotType": "STACKED_AREA", "targetAxis": "Y1", "legendTemplate": "${metric.label.agent_name}"}], "timeshiftDuration": "0s", "yAxis": {"label": "Requests/sec", "scale": "LINEAR"}, "chartOptions": {"mode": "COLOR"}}}}, {"xPos": 6, "yPos": 4, "width": 6, "height": 4, "widget": {"title": "Error Rate by Model", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"bigquery_dataset\"", "aggregation": {"alignmentPeriod": "300s", "perSeriesAligner": "ALIGN_MEAN", "crossSeriesReducer": "REDUCE_MEAN", "groupByFields": ["metric.label.model"]}}}, "plotType": "LINE", "targetAxis": "Y1", "legendTemplate": "${metric.label.model}"}], "timeshiftDuration": "0s", "yAxis": {"label": "Error Rate (%)", "scale": "LINEAR"}, "chartOptions": {"mode": "COLOR"}, "thresholds": [{"value": 5.0, "color": "RED", "direction": "ABOVE", "label": "Critical Threshold"}, {"value": 1.0, "color": "YELLOW", "direction": "ABOVE", "label": "Warning Threshold"}]}}}, {"yPos": 8, "width": 12, "height": 6, "widget": {"title": "Top 10 Slowest Agents (P95 Latency)", "table": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"bigquery_dataset\"", "aggregation": {"alignmentPeriod": "3600s", "perSeriesAligner": "ALIGN_PERCENTILE_95", "crossSeriesReducer": "REDUCE_PERCENTILE_95", "groupByFields": ["metric.label.agent_name", "metric.label.model", "metric.label.environment"]}}}, "tableTemplate": "${metric.label.agent_name} (${metric.label.model})", "tableDisplayOptions": {"shownColumns": ["METRIC_LABELS", "VALUE", "TIME_SERIES"]}}], "columnSettings": [{"column": "VALUE", "displayName": "P95 Latency (ms)"}, {"column": "METRIC_LABELS", "displayName": "Agent (Model)"}]}}}, {"yPos": 14, "width": 6, "height": 4, "widget": {"title": "Token Processing Rate", "scorecard": {"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"bigquery_dataset\"", "aggregation": {"alignmentPeriod": "300s", "perSeriesAligner": "ALIGN_RATE", "crossSeriesReducer": "REDUCE_SUM"}}, "unitOverride": "tokens/sec"}, "sparkChartView": {"sparkChartType": "SPARK_LINE"}, "gaugeView": {"lowerBound": 0, "upperBound": 10000}}}}, {"xPos": 6, "yPos": 14, "width": 6, "height": 4, "widget": {"title": "Average Response Time", "scorecard": {"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"bigquery_dataset\"", "aggregation": {"alignmentPeriod": "300s", "perSeriesAligner": "ALIGN_MEAN", "crossSeriesReducer": "REDUCE_MEAN"}}, "unitOverride": "ms"}, "sparkChartView": {"sparkChartType": "SPARK_LINE"}, "thresholds": [{"value": 2000, "color": "GREEN", "direction": "BELOW"}, {"value": 5000, "color": "YELLOW", "direction": "BELOW"}]}}}, {"yPos": 18, "width": 12, "height": 4, "widget": {"title": "Latency Distribution Heatmap", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"bigquery_dataset\"", "aggregation": {"alignmentPeriod": "300s", "perSeriesAligner": "ALIGN_DELTA", "crossSeriesReducer": "REDUCE_SUM", "groupByFields": ["metric.label.latency_bucket"]}}}, "plotType": "STACKED_BAR", "targetAxis": "Y1", "legendTemplate": "${metric.label.latency_bucket}ms"}], "timeshiftDuration": "0s", "yAxis": {"label": "Request Count", "scale": "LINEAR"}, "chartOptions": {"mode": "COLOR"}}}}, {"yPos": 22, "width": 6, "height": 4, "widget": {"title": "Model Efficiency Score", "scorecard": {"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"bigquery_dataset\"", "aggregation": {"alignmentPeriod": "3600s", "perSeriesAligner": "ALIGN_MEAN", "crossSeriesReducer": "REDUCE_MEAN"}}, "unitOverride": "%"}, "sparkChartView": {"sparkChartType": "SPARK_BAR"}, "thresholds": [{"value": 40, "color": "GREEN", "direction": "ABOVE"}, {"value": 20, "color": "YELLOW", "direction": "ABOVE"}]}}}, {"xPos": 6, "yPos": 22, "width": 6, "height": 4, "widget": {"title": "Concurrent Agent Instances", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"bigquery_dataset\"", "aggregation": {"alignmentPeriod": "300s", "perSeriesAligner": "ALIGN_MAX", "crossSeriesReducer": "REDUCE_COUNT", "groupByFields": ["metric.label.environment"]}}}, "plotType": "STACKED_AREA", "targetAxis": "Y1", "legendTemplate": "${metric.label.environment}"}], "timeshiftDuration": "0s", "yAxis": {"label": "Active Instances", "scale": "LINEAR"}, "chartOptions": {"mode": "COLOR"}}}}]}, "labels": {"team": "ai-foundry", "environment": "production", "dashboard_type": "performance"}}