{"displayName": "Metamorphic Foundry - Policy & Compliance", "mosaicLayout": {"tiles": [{"width": 12, "height": 4, "widget": {"title": "Compliance Score Overview", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"bigquery_dataset\"", "aggregation": {"alignmentPeriod": "3600s", "perSeriesAligner": "ALIGN_MEAN", "crossSeriesReducer": "REDUCE_MEAN", "groupByFields": ["metric.label.compliance_type"]}}}, "plotType": "LINE", "targetAxis": "Y1", "legendTemplate": "${metric.label.compliance_type}"}], "timeshiftDuration": "0s", "yAxis": {"label": "Compliance Score (%)", "scale": "LINEAR"}, "chartOptions": {"mode": "COLOR"}, "thresholds": [{"value": 95, "color": "GREEN", "direction": "ABOVE", "label": "Target Compliance"}, {"value": 80, "color": "YELLOW", "direction": "ABOVE", "label": "Warning Threshold"}]}}}, {"yPos": 4, "width": 6, "height": 4, "widget": {"title": "Policy Violations by Severity", "pieChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"bigquery_dataset\"", "aggregation": {"alignmentPeriod": "86400s", "perSeriesAligner": "ALIGN_SUM", "crossSeriesReducer": "REDUCE_SUM", "groupByFields": ["metric.label.severity"]}}}, "legendTemplate": "${metric.label.severity}"}], "chartType": "DONUT", "showLabels": true}}}, {"xPos": 6, "yPos": 4, "width": 6, "height": 4, "widget": {"title": "Model Armor Block Rate", "scorecard": {"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"bigquery_dataset\"", "aggregation": {"alignmentPeriod": "3600s", "perSeriesAligner": "ALIGN_MEAN", "crossSeriesReducer": "REDUCE_MEAN"}}, "unitOverride": "%"}, "sparkChartView": {"sparkChartType": "SPARK_LINE"}, "thresholds": [{"value": 1.0, "color": "GREEN", "direction": "BELOW"}, {"value": 5.0, "color": "YELLOW", "direction": "BELOW"}]}}}, {"yPos": 8, "width": 12, "height": 6, "widget": {"title": "Agent Compliance Status", "table": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"bigquery_dataset\"", "aggregation": {"alignmentPeriod": "86400s", "perSeriesAligner": "ALIGN_MEAN", "crossSeriesReducer": "REDUCE_MEAN", "groupByFields": ["metric.label.agent_name", "metric.label.compliance_status"]}}}, "tableTemplate": "${metric.label.agent_name}", "tableDisplayOptions": {"shownColumns": ["METRIC_LABELS", "VALUE", "TIME_SERIES"]}}], "columnSettings": [{"column": "VALUE", "displayName": "Compliance Score"}, {"column": "METRIC_LABELS", "displayName": "Agent Name"}]}}}, {"yPos": 14, "width": 6, "height": 4, "widget": {"title": "Hallucination Rate Compliance", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"bigquery_dataset\"", "aggregation": {"alignmentPeriod": "3600s", "perSeriesAligner": "ALIGN_MEAN", "crossSeriesReducer": "REDUCE_MEAN", "groupByFields": ["metric.label.agent_name"]}}}, "plotType": "LINE", "targetAxis": "Y1", "legendTemplate": "${metric.label.agent_name}"}], "timeshiftDuration": "0s", "yAxis": {"label": "Hallucination Rate (%)", "scale": "LINEAR"}, "chartOptions": {"mode": "COLOR"}, "thresholds": [{"value": 2.0, "color": "RED", "direction": "ABOVE", "label": "SLA Violation"}]}}}, {"xPos": 6, "yPos": 14, "width": 6, "height": 4, "widget": {"title": "Toxicity Score Compliance", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"bigquery_dataset\"", "aggregation": {"alignmentPeriod": "3600s", "perSeriesAligner": "ALIGN_MEAN", "crossSeriesReducer": "REDUCE_MEAN", "groupByFields": ["metric.label.agent_name"]}}}, "plotType": "LINE", "targetAxis": "Y1", "legendTemplate": "${metric.label.agent_name}"}], "timeshiftDuration": "0s", "yAxis": {"label": "Toxicity Score", "scale": "LINEAR"}, "chartOptions": {"mode": "COLOR"}, "thresholds": [{"value": 0.05, "color": "RED", "direction": "ABOVE", "label": "SLA Violation"}]}}}, {"yPos": 18, "width": 12, "height": 4, "widget": {"title": "Policy Violation Trends", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"bigquery_dataset\"", "aggregation": {"alignmentPeriod": "3600s", "perSeriesAligner": "ALIGN_SUM", "crossSeriesReducer": "REDUCE_SUM", "groupByFields": ["metric.label.violation_type"]}}}, "plotType": "STACKED_BAR", "targetAxis": "Y1", "legendTemplate": "${metric.label.violation_type}"}], "timeshiftDuration": "0s", "yAxis": {"label": "Violation Count", "scale": "LINEAR"}, "chartOptions": {"mode": "COLOR"}}}}, {"yPos": 22, "width": 6, "height": 4, "widget": {"title": "A2A Protocol Compliance", "scorecard": {"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"bigquery_dataset\"", "aggregation": {"alignmentPeriod": "86400s", "perSeriesAligner": "ALIGN_MEAN", "crossSeriesReducer": "REDUCE_MEAN"}}, "unitOverride": "%"}, "sparkChartView": {"sparkChartType": "SPARK_BAR"}, "thresholds": [{"value": 100, "color": "GREEN", "direction": "ABOVE"}, {"value": 95, "color": "YELLOW", "direction": "ABOVE"}]}}}, {"xPos": 6, "yPos": 22, "width": 6, "height": 4, "widget": {"title": "Audit Coverage", "scorecard": {"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"bigquery_dataset\"", "aggregation": {"alignmentPeriod": "86400s", "perSeriesAligner": "ALIGN_MEAN", "crossSeriesReducer": "REDUCE_MEAN"}}, "unitOverride": "%"}, "sparkChartView": {"sparkChartType": "SPARK_LINE"}, "thresholds": [{"value": 100, "color": "GREEN", "direction": "ABOVE"}, {"value": 95, "color": "YELLOW", "direction": "ABOVE"}]}}}, {"yPos": 26, "width": 12, "height": 6, "widget": {"title": "Compliance Actions & Remediation", "text": {"content": "## Policy Compliance Dashboard\n\n### Current Compliance Status\n- **Overall Score**: Monitor real-time compliance across all agents\n- **Policy Violations**: Track and categorize violations by severity\n- **Model Armor**: Content safety and security enforcement\n\n### Key Metrics\n- **Hallucination Rate**: Must be ≤ 2% per SLA\n- **Toxicity Score**: Must be ≤ 0.05 per SLA\n- **Audit Coverage**: Target 100% of agent interactions\n- **A2A Protocol**: Secure agent-to-agent communication compliance\n\n### Quick Actions\n- [View Violation Details](https://console.cloud.google.com/bigquery)\n- [Policy Configuration](#)\n- [Incident Response Runbook](#)\n- [Compliance Report Generator](#)", "format": "MARKDOWN"}}}]}, "labels": {"team": "ai-foundry", "environment": "production", "dashboard_type": "compliance"}}