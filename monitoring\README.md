# Metamorphic Foundry Monitoring & Alerting

Comprehensive monitoring and alerting system for the Metamorphic Foundry platform, implementing SLO-based monitoring with automated alerting and observability.

## Overview

The monitoring system provides:

- **SLO-based Monitoring**: Service Level Objectives for quality, performance, and availability
- **Real-time Alerting**: Multi-channel alerting with burn rate detection
- **Custom Metrics**: Foundry-specific metrics for agent performance
- **Observability Pipeline**: BigQuery integration for analytics and reporting
- **Dashboard Integration**: Looker dashboards for visualization

## Architecture

```
Agent Interactions → Pub/Sub → BigQuery → Cloud Monitoring → Alerts
                                    ↓
                              Looker Dashboards
```

## SLO Targets

### Quality SLOs
- **Hallucination Rate**: ≤ 2% (98% of requests)
- **Toxicity Score**: ≤ 0.05 (95% of requests)
- **Policy Compliance**: 95% compliance rate

### Performance SLOs
- **Gemini Flash**: < 1s P95 latency (95% of requests)
- **Gemini Pro**: < 8s P95 latency (95% of requests)
- **Flash Lite**: < 500ms P95 latency (95% of requests)
- **Gemma Edge**: < 2s P95 latency (95% of requests)

### Availability SLOs
- **Agent Availability**: 99.9% uptime
- **Deployment Success**: 95% success rate

### Cost Efficiency SLOs
- **Model Efficiency**: 40% Flash/Gemma usage
- **Cost Per Turn**: 90% of turns < $0.01

## Setup

### 1. Prerequisites

```bash
# Enable required APIs
gcloud services enable monitoring.googleapis.com
gcloud services enable logging.googleapis.com
gcloud services enable bigquery.googleapis.com
gcloud services enable pubsub.googleapis.com
```

### 2. Deploy Infrastructure

```bash
# Deploy monitoring infrastructure
cd terraform
terraform apply -target=google_monitoring_service.foundry_agents
terraform apply -target=google_monitoring_slo
terraform apply -target=google_monitoring_alert_policy
```

### 3. Setup Monitoring

```bash
# Run monitoring setup script
./scripts/setup_monitoring.sh YOUR_PROJECT_ID production
```

### 4. Configure Notification Channels

```bash
# Create email notification channel
gcloud alpha monitoring channels create \
  --display-name="Foundry Email Alerts" \
  --type=email \
  --channel-labels=email_address=<EMAIL>

# Create Slack notification channel (requires webhook URL)
gcloud alpha monitoring channels create \
  --display-name="Foundry Slack Alerts" \
  --type=slack \
  --channel-labels=url=YOUR_SLACK_WEBHOOK_URL
```

## Custom Metrics

### Agent Turn Latency
```
Metric: foundry/agent_turn_latency
Type: GAUGE
Unit: milliseconds
Labels: agent_name, model, environment
```

### Hallucination Rate
```
Metric: foundry/hallucination_rate
Type: GAUGE
Unit: ratio (0.0-1.0)
Labels: agent_name
```

### Toxicity Score
```
Metric: foundry/toxicity_score
Type: GAUGE
Unit: ratio (0.0-1.0)
Labels: agent_name
```

### Cost Tracking
```
Metric: foundry/cost_tracking
Type: GAUGE
Unit: USD
Labels: agent_name, model
```

## Alert Policies

### Critical Alerts (PagerDuty)
- **Hallucination SLO Burn**: 10x burn rate for 5 minutes
- **Toxicity SLO Burn**: 10x burn rate for 5 minutes
- **Availability SLO Burn**: 20x burn rate for 2 minutes
- **High Error Rate**: >5% error rate for 5 minutes

### Warning Alerts (Slack/Email)
- **Latency P95 Warning**: Approaching SLA thresholds
- **Model Armor Blocks**: >2% block rate for 15 minutes
- **Deployment Failures**: >10% failure rate for 10 minutes
- **Cost Anomaly**: Daily cost increase >$100

## SLO Burn Rate Calculation

Burn rate indicates how fast you're consuming your error budget:

```
Burn Rate = (Error Rate) / (1 - SLO Target)

Examples:
- SLO: 99.9% availability (0.1% error budget)
- Current error rate: 1%
- Burn rate: 1% / 0.1% = 10x

At 10x burn rate, you'll exhaust your 30-day error budget in 3 days.
```

### Burn Rate Thresholds

| Window | Threshold | Action | Budget Consumption |
|--------|-----------|--------|-------------------|
| 1 hour | 14.4x | Page | 2% of 30-day budget |
| 6 hours | 6.0x | Ticket | 10% of 30-day budget |
| 24 hours | 3.0x | Alert | 33% of 30-day budget |
| 72 hours | 1.0x | Monitor | 100% of 30-day budget |

## Monitoring Queries

### BigQuery Queries

```sql
-- Agent performance summary
SELECT
  agent_name,
  model,
  COUNT(*) as total_turns,
  AVG(processing_time_ms) as avg_latency,
  APPROX_QUANTILES(processing_time_ms, 100)[OFFSET(95)] as p95_latency,
  AVG(hallucination_rate) as avg_hallucination_rate,
  AVG(toxicity_score) as avg_toxicity_score
FROM `project.foundry_observability.agent_turns`
WHERE timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 24 HOUR)
GROUP BY agent_name, model
ORDER BY total_turns DESC;

-- SLO compliance check
SELECT
  DATE(timestamp) as date,
  COUNTIF(hallucination_rate <= 0.02) / COUNT(*) * 100 as hallucination_compliance_pct,
  COUNTIF(toxicity_score <= 0.05) / COUNT(*) * 100 as toxicity_compliance_pct,
  COUNTIF(processing_time_ms <= 1000 AND model = 'gemini-2.5-flash') / 
    COUNTIF(model = 'gemini-2.5-flash') * 100 as flash_latency_compliance_pct
FROM `project.foundry_observability.agent_turns`
WHERE timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 7 DAY)
GROUP BY DATE(timestamp)
ORDER BY date DESC;
```

### Cloud Monitoring Queries

```
# P95 latency by model
fetch bigquery_dataset
| filter resource.label.dataset_id == "foundry_observability"
| group_by [resource.label.model], 1m
| value val(95)

# Error rate
fetch bigquery_dataset
| filter resource.label.dataset_id == "foundry_observability"
| group_by [], 5m
| fraction_true val() > 0 AND error_message != ""

# Hallucination rate compliance
fetch bigquery_dataset
| filter resource.label.dataset_id == "foundry_observability"
| group_by [], 1h
| fraction_true hallucination_rate <= 0.02
```

## Dashboard URLs

- **Executive Overview**: High-level KPIs and SLO status
- **Performance Dashboard**: Latency, throughput, and model efficiency
- **Compliance Dashboard**: Policy violations and quality metrics
- **Deployment Dashboard**: CI/CD pipeline health and success rates

## Troubleshooting

### Common Issues

**No Metrics Appearing**
```bash
# Check if log sink is working
gcloud logging sinks describe foundry-observability-sink

# Verify BigQuery dataset permissions
bq show foundry_observability

# Check custom metrics
gcloud logging metrics list --filter="name:foundry"
```

**Alerts Not Firing**
```bash
# Check notification channels
gcloud alpha monitoring channels list

# Verify alert policy conditions
gcloud alpha monitoring policies list

# Test notification channel
gcloud alpha monitoring channels verify CHANNEL_ID
```

**SLO Burn Rate Issues**
```bash
# Check SLO configuration
gcloud alpha monitoring slos list

# View SLO burn rate
gcloud alpha monitoring slos describe SLO_NAME
```

### Debug Commands

```bash
# View recent logs
gcloud logging read 'resource.type="bigquery_dataset"' --limit=10

# Check metric values
gcloud logging metrics describe foundry_agent_turn_latency

# Test alert policy
gcloud alpha monitoring policies test POLICY_NAME

# View SLO status
gcloud alpha monitoring slos describe SLO_NAME --format="table(goal,rollingPeriod,serviceLevelIndicator)"
```

## Maintenance

### Regular Tasks

**Weekly**
- Review SLO burn rates and error budgets
- Check alert policy effectiveness
- Validate dashboard accuracy
- Update notification channels if needed

**Monthly**
- Review and adjust SLO targets based on performance
- Analyze cost trends and efficiency metrics
- Update alert thresholds based on operational experience
- Conduct SLO review meetings with stakeholders

**Quarterly**
- Comprehensive monitoring system review
- Update SLO targets based on business requirements
- Review and optimize custom metrics
- Disaster recovery testing for monitoring systems

## Integration with CI/CD

The monitoring system integrates with the CI/CD pipeline:

1. **Pre-deployment**: SLO compliance checks
2. **Deployment**: Automated canary analysis
3. **Post-deployment**: SLO monitoring and alerting
4. **Rollback**: Automated rollback on SLO violations

## Cost Optimization

Monitor and optimize monitoring costs:

- Use sampling for high-volume metrics
- Set appropriate retention periods
- Optimize BigQuery queries
- Use efficient alert conditions
- Regular cleanup of unused resources

## Security Considerations

- Secure notification channels (encrypted webhooks)
- Limit access to monitoring data
- Audit monitoring configuration changes
- Protect sensitive metrics and logs
- Regular security reviews of monitoring setup
