# Metamorphic Foundry SLO Configuration
# Defines Service Level Objectives for agent performance and quality

# Global SLO settings
global:
  project_id: "${PROJECT_ID}"
  service_name: "foundry-agents"
  default_rolling_period_days: 30
  default_goal: 0.95  # 95% default target

# Quality SLOs
quality_slos:
  hallucination_rate:
    display_name: "Agent Hallucination Rate SLO"
    description: "Percentage of agent responses with hallucination rate ≤ 2%"
    goal: 0.98  # 98% of responses should have ≤ 2% hallucination rate
    rolling_period_days: 30
    sli_type: "request_based"
    filter:
      total: 'resource.type="bigquery_dataset" AND jsonPayload.hallucination_rate IS NOT NULL'
      good: 'resource.type="bigquery_dataset" AND jsonPayload.hallucination_rate <= 0.02'
    alert_thresholds:
      critical: 10.0  # 10x burn rate
      warning: 5.0    # 5x burn rate
    
  toxicity_score:
    display_name: "Agent Toxicity Score SLO"
    description: "Percentage of agent responses with toxicity score ≤ 0.05"
    goal: 0.95  # 95% of responses should have ≤ 0.05 toxicity score
    rolling_period_days: 30
    sli_type: "request_based"
    filter:
      total: 'resource.type="bigquery_dataset" AND jsonPayload.toxicity_score IS NOT NULL'
      good: 'resource.type="bigquery_dataset" AND jsonPayload.toxicity_score <= 0.05'
    alert_thresholds:
      critical: 10.0
      warning: 5.0

# Performance SLOs
performance_slos:
  flash_latency:
    display_name: "Gemini Flash Latency SLO"
    description: "95% of Flash model requests complete within 1 second"
    goal: 0.95
    rolling_period_days: 7  # Shorter period for latency
    sli_type: "distribution_cut"
    filter: 'resource.type="bigquery_dataset" AND jsonPayload.model="gemini-2.5-flash"'
    threshold_ms: 1000  # 1 second
    alert_thresholds:
      critical: 15.0  # 15x burn rate
      warning: 10.0   # 10x burn rate
    
  pro_latency:
    display_name: "Gemini Pro Latency SLO"
    description: "95% of Pro model requests complete within 8 seconds"
    goal: 0.95
    rolling_period_days: 7
    sli_type: "distribution_cut"
    filter: 'resource.type="bigquery_dataset" AND jsonPayload.model="gemini-2.5-pro"'
    threshold_ms: 8000  # 8 seconds
    alert_thresholds:
      critical: 15.0
      warning: 10.0
    
  flash_lite_latency:
    display_name: "Gemini Flash Lite Latency SLO"
    description: "95% of Flash Lite model requests complete within 500ms"
    goal: 0.95
    rolling_period_days: 7
    sli_type: "distribution_cut"
    filter: 'resource.type="bigquery_dataset" AND jsonPayload.model="gemini-2.5-flash-lite"'
    threshold_ms: 500  # 500 milliseconds
    alert_thresholds:
      critical: 15.0
      warning: 10.0
    
  gemma_latency:
    display_name: "Gemma Edge Latency SLO"
    description: "95% of Gemma model requests complete within 2 seconds"
    goal: 0.95
    rolling_period_days: 7
    sli_type: "distribution_cut"
    filter: 'resource.type="bigquery_dataset" AND jsonPayload.model="gemma-3n"'
    threshold_ms: 2000  # 2 seconds
    alert_thresholds:
      critical: 15.0
      warning: 10.0

# Availability SLOs
availability_slos:
  agent_availability:
    display_name: "Agent Availability SLO"
    description: "99.9% of agent requests succeed without errors"
    goal: 0.999  # 99.9% availability
    rolling_period_days: 30
    sli_type: "request_based"
    filter:
      total: 'resource.type="bigquery_dataset"'
      good: 'resource.type="bigquery_dataset" AND (jsonPayload.error_message IS NULL OR jsonPayload.error_message="")'
    alert_thresholds:
      critical: 20.0  # 20x burn rate for availability
      warning: 10.0
    
  deployment_success:
    display_name: "Deployment Success Rate SLO"
    description: "95% of deployments succeed without rollback"
    goal: 0.95
    rolling_period_days: 7
    sli_type: "request_based"
    filter:
      total: 'resource.type="bigquery_dataset" AND jsonPayload.deployment_status IS NOT NULL'
      good: 'resource.type="bigquery_dataset" AND jsonPayload.deployment_status="success"'
    alert_thresholds:
      critical: 10.0
      warning: 5.0

# Cost Efficiency SLOs
cost_slos:
  model_efficiency:
    display_name: "Model Efficiency SLO"
    description: "40% of requests use efficient models (Flash/Gemma)"
    goal: 0.40  # 40% efficient model usage
    rolling_period_days: 7
    sli_type: "request_based"
    filter:
      total: 'resource.type="bigquery_dataset" AND jsonPayload.model IS NOT NULL'
      good: 'resource.type="bigquery_dataset" AND (jsonPayload.model="gemini-2.5-flash" OR jsonPayload.model="gemini-2.5-flash-lite" OR jsonPayload.model="gemma-3n")'
    alert_thresholds:
      warning: 5.0  # Only warning for efficiency
    
  cost_per_turn:
    display_name: "Cost Per Turn SLO"
    description: "Average cost per turn stays below $0.01"
    goal: 0.90  # 90% of turns should be under $0.01
    rolling_period_days: 7
    sli_type: "request_based"
    filter:
      total: 'resource.type="bigquery_dataset" AND jsonPayload.cost_usd > 0'
      good: 'resource.type="bigquery_dataset" AND jsonPayload.cost_usd <= 0.01'
    alert_thresholds:
      warning: 5.0

# Security and Compliance SLOs
security_slos:
  model_armor_effectiveness:
    display_name: "Model Armor Block Rate SLO"
    description: "Model Armor block rate stays below 1%"
    goal: 0.99  # 99% of requests should not be blocked
    rolling_period_days: 7
    sli_type: "request_based"
    filter:
      total: 'resource.type="bigquery_dataset" AND jsonPayload.model_armor_verdict IS NOT NULL'
      good: 'resource.type="bigquery_dataset" AND jsonPayload.model_armor_verdict != "BLOCK"'
    alert_thresholds:
      warning: 5.0
    
  policy_compliance:
    display_name: "Policy Compliance SLO"
    description: "95% of agent interactions comply with all policies"
    goal: 0.95
    rolling_period_days: 30
    sli_type: "request_based"
    filter:
      total: 'resource.type="bigquery_dataset"'
      good: 'resource.type="bigquery_dataset" AND jsonPayload.hallucination_rate <= 0.02 AND jsonPayload.toxicity_score <= 0.05'
    alert_thresholds:
      critical: 10.0
      warning: 5.0

# User Experience SLOs
user_experience_slos:
  user_satisfaction:
    display_name: "User Satisfaction SLO"
    description: "Average user satisfaction score ≥ 4.0/5.0"
    goal: 0.80  # 80% of rated interactions should be ≥ 4.0
    rolling_period_days: 30
    sli_type: "request_based"
    filter:
      total: 'resource.type="bigquery_dataset" AND jsonPayload.user_satisfaction IS NOT NULL'
      good: 'resource.type="bigquery_dataset" AND jsonPayload.user_satisfaction >= 4.0'
    alert_thresholds:
      warning: 5.0
    
  response_relevance:
    display_name: "Response Relevance SLO"
    description: "90% of responses are relevant to user queries"
    goal: 0.90
    rolling_period_days: 7
    sli_type: "request_based"
    filter:
      total: 'resource.type="bigquery_dataset" AND jsonPayload.relevance_score IS NOT NULL'
      good: 'resource.type="bigquery_dataset" AND jsonPayload.relevance_score >= 0.8'
    alert_thresholds:
      warning: 5.0

# SLO Reporting Configuration
reporting:
  frequency: "daily"  # daily, weekly, monthly
  recipients:
    - "<EMAIL>"
    - "<EMAIL>"
  
  dashboard_links:
    executive: "https://console.cloud.google.com/monitoring/dashboards/custom/executive"
    performance: "https://console.cloud.google.com/monitoring/dashboards/custom/performance"
    compliance: "https://console.cloud.google.com/monitoring/dashboards/custom/compliance"
  
  slo_burn_rate_windows:
    - name: "1h"
      duration: "3600s"
      threshold_multiplier: 14.4  # 1% budget in 1 hour
    - name: "6h"
      duration: "21600s"
      threshold_multiplier: 6.0   # 1% budget in 6 hours
    - name: "24h"
      duration: "86400s"
      threshold_multiplier: 3.0   # 1% budget in 24 hours
    - name: "72h"
      duration: "259200s"
      threshold_multiplier: 1.0   # 1% budget in 72 hours

# Error Budget Policies
error_budget:
  policies:
    - name: "Fast Burn"
      description: "Consume 2% of 30-day error budget in 1 hour"
      condition: "burn_rate > 14.4 for 2 minutes"
      action: "page"
      
    - name: "Slow Burn"
      description: "Consume 10% of 30-day error budget in 6 hours"
      condition: "burn_rate > 6.0 for 15 minutes"
      action: "ticket"
      
    - name: "Budget Exhaustion Warning"
      description: "Error budget will be exhausted in 4 hours"
      condition: "remaining_budget < 10%"
      action: "alert"

# Integration Settings
integrations:
  bigquery:
    dataset: "foundry_observability"
    slo_table: "slo_metrics"
    burn_rate_table: "slo_burn_rates"
    
  pubsub:
    slo_alerts_topic: "slo-alerts"
    burn_rate_topic: "slo-burn-rates"
    
  slack:
    channels:
      critical: "#foundry-critical"
      warnings: "#foundry-alerts"
      reports: "#foundry-slo-reports"
