# Metamorphic Foundry Blueprint Policy
# Implements Evaluation Playbook §5.2: Policy-as-Code validation
# Validates agent blueprints against security, performance, and governance requirements

package blueprint

import rego.v1

# Default policy decisions
default allow := false
default valid := false

# Main validation entry point
allow if {
    valid_blueprint
    security_compliant
    performance_acceptable
    governance_approved
}

valid if allow

# Blueprint structure validation
valid_blueprint if {
    # Required fields present
    input.name
    input.version
    input.model
    input.instructions
    
    # Name follows kebab-case convention
    regex.match("^[a-z0-9]+(-[a-z0-9]+)*$", input.name)
    
    # Version follows semantic versioning
    semver.is_valid(input.version)
    
    # Model is from approved list
    input.model in approved_models
    
    # Instructions within token limits
    instruction_token_count <= max_instruction_tokens
}

# Security compliance checks
security_compliant if {
    # Model Armor policy is required and valid
    input.security.model_armor_policy
    input.security.model_armor_policy in valid_armor_policies
    
    # DLP templates are valid if specified
    dlp_templates_valid
    
    # No hardcoded secrets in instructions
    not contains_secrets
    
    # A2A capabilities are properly configured for multi-agent scenarios
    a2a_properly_configured
}

# Performance acceptability checks
performance_acceptable if {
    # Model selection appropriate for use case
    model_selection_appropriate
    
    # Token limits respected for target model
    token_limits_respected
    
    # Memory configuration reasonable
    memory_config_reasonable
    
    # Tool count within limits
    tool_count_acceptable
}

# Governance approval checks
governance_approved if {
    # Required evaluation metrics specified
    required_metrics_present
    
    # Metadata properly filled
    metadata_complete
    
    # Owner and squad specified for accountability
    input.metadata.owner
    input.metadata.squad
    
    # No prohibited content in instructions
    not contains_prohibited_content
}

# Helper rules and data

approved_models := {
    "gemini-2.5-pro",
    "gemini-2.5-flash", 
    "gemini-2.5-flash-lite",
    "gemma-3n",
    "external"
}

valid_armor_policies := {
    "FRAUD_LOW",
    "FRAUD_MED", 
    "FRAUD_HIGH",
    "PII_LOW",
    "PII_MED",
    "PII_HIGH"
}

# Token counting (simplified - in production would use actual tokenizer)
instruction_token_count := count(split(input.instructions, " "))

max_instruction_tokens := 8192 if input.model == "gemini-2.5-flash-lite"
max_instruction_tokens := 32768 if input.model in {"gemini-2.5-flash", "gemini-2.5-pro"}
max_instruction_tokens := 16384 if input.model == "gemma-3n"
max_instruction_tokens := 65536 if input.model == "external"

# DLP template validation
dlp_templates_valid if {
    not input.security.dlp_templates
}

dlp_templates_valid if {
    input.security.dlp_templates
    every template in input.security.dlp_templates {
        template in valid_dlp_templates
    }
}

valid_dlp_templates := {
    "CREDIT_CARD_NUMBER",
    "EMAIL_ADDRESS", 
    "PHONE_NUMBER",
    "SSN",
    "PASSPORT_NUMBER",
    "DRIVER_LICENSE_NUMBER"
}

# Secret detection (basic patterns)
contains_secrets if {
    secret_patterns := [
        "(?i)(password|passwd|pwd)\\s*[=:]\\s*['\"][^'\"]+['\"]",
        "(?i)(api[_-]?key|apikey)\\s*[=:]\\s*['\"][^'\"]+['\"]",
        "(?i)(secret|token)\\s*[=:]\\s*['\"][^'\"]+['\"]",
        "(?i)(access[_-]?key|accesskey)\\s*[=:]\\s*['\"][^'\"]+['\"]"
    ]
    
    some pattern in secret_patterns
    regex.match(pattern, input.instructions)
}

# A2A configuration validation
a2a_properly_configured if {
    # Single-tool agents don't need A2A
    count(input.tools) <= 1
}

a2a_properly_configured if {
    # Multi-tool agents should have A2A config
    count(input.tools) > 1
    input.a2a.required_capabilities
    count(input.a2a.required_capabilities) > 0
    
    # All capabilities are valid
    every capability in input.a2a.required_capabilities {
        capability in valid_a2a_capabilities
    }
}

valid_a2a_capabilities := {
    "memory.put",
    "memory.get", 
    "tool.call",
    "message.send",
    "message.receive"
}

# Model selection appropriateness
model_selection_appropriate if {
    # Flash-Lite for simple chat/routing
    input.model == "gemini-2.5-flash-lite"
    instruction_token_count <= 4000
    count(input.tools) <= 2
}

model_selection_appropriate if {
    # Flash for general purpose
    input.model == "gemini-2.5-flash"
    instruction_token_count <= 16000
}

model_selection_appropriate if {
    # Pro for complex reasoning
    input.model == "gemini-2.5-pro"
    complex_reasoning_indicators
}

model_selection_appropriate if {
    # Gemma for edge deployment
    input.model == "gemma-3n"
    edge_deployment_indicators
}

model_selection_appropriate if {
    # External models allowed with justification
    input.model == "external"
    input.metadata.description
    contains(lower(input.metadata.description), "external")
}

complex_reasoning_indicators if {
    reasoning_keywords := [
        "analyze", "critique", "review", "evaluate", "assess",
        "compare", "synthesize", "reason", "logic", "complex"
    ]
    
    some keyword in reasoning_keywords
    contains(lower(input.instructions), keyword)
}

edge_deployment_indicators if {
    edge_keywords := ["edge", "offline", "local", "embedded", "iot"]
    
    some keyword in edge_keywords
    contains(lower(input.instructions), keyword)
}

# Token limits validation
token_limits_respected if {
    instruction_token_count <= max_instruction_tokens
}

# Memory configuration validation
memory_config_reasonable if {
    not input.memory
}

memory_config_reasonable if {
    input.memory
    input.memory.retention_days >= 1
    input.memory.retention_days <= 400
    input.memory.store in valid_memory_stores
}

valid_memory_stores := {"firestore", "cloud-sql-postgres", "sqlite"}

# Tool count validation
tool_count_acceptable if {
    count(input.tools) <= 10  # Reasonable limit for agent complexity
}

# Required evaluation metrics
required_metrics_present if {
    input.evaluation.metrics
    count(input.evaluation.metrics) > 0
    
    # Check for required metric types
    has_hallucination_metric
    has_toxicity_metric
}

has_hallucination_metric if {
    some metric in input.evaluation.metrics
    contains(lower(metric), "hallucination")
}

has_toxicity_metric if {
    some metric in input.evaluation.metrics
    contains(lower(metric), "toxicity")
}

# Metadata completeness
metadata_complete if {
    input.metadata.description
    count(input.metadata.description) > 10  # Meaningful description
    input.metadata.tags
    count(input.metadata.tags) > 0
}

# Prohibited content detection
contains_prohibited_content if {
    prohibited_terms := [
        "hack", "exploit", "malware", "virus", "attack",
        "illegal", "fraud", "scam", "phishing", "spam"
    ]
    
    some term in prohibited_terms
    contains(lower(input.instructions), term)
}

# Violation reporting
violations contains msg if {
    not valid_blueprint
    msg := "Blueprint structure validation failed"
}

violations contains msg if {
    not security_compliant
    msg := "Security compliance checks failed"
}

violations contains msg if {
    not performance_acceptable
    msg := "Performance requirements not met"
}

violations contains msg if {
    not governance_approved
    msg := "Governance approval requirements not satisfied"
}

violations contains msg if {
    contains_secrets
    msg := "Hardcoded secrets detected in instructions"
}

violations contains msg if {
    contains_prohibited_content
    msg := "Prohibited content detected in instructions"
}

violations contains msg if {
    instruction_token_count > max_instruction_tokens
    msg := sprintf("Instruction token count (%d) exceeds limit (%d) for model %s", 
                   [instruction_token_count, max_instruction_tokens, input.model])
}
