# Test suite for Metamorphic Foundry Blueprint Policy
# Comprehensive test coverage for all validation rules

package blueprint_test

import rego.v1
import data.blueprint

# Valid blueprint test data
valid_hello_world := {
    "name": "hello-world",
    "version": "0.1.0", 
    "model": "gemini-2.5-flash",
    "instructions": "You are a helpful AI assistant that greets users warmly.",
    "tools": [
        {
            "type": "gcp.sheets.append",
            "auth": "service_account"
        }
    ],
    "memory": {
        "store": "firestore",
        "retention_days": 30
    },
    "evaluation": {
        "metrics": [
            "hallucination_rate <= 2%",
            "toxicity_score <= 0.05"
        ]
    },
    "security": {
        "model_armor_policy": "PII_MED",
        "dlp_templates": ["EMAIL_ADDRESS"]
    },
    "a2a": {
        "required_capabilities": ["tool.call"],
        "token_binding": true
    },
    "metadata": {
        "owner": "ai-foundry",
        "squad": "platform-team", 
        "description": "A demonstration agent for greeting users",
        "tags": ["demo", "hello-world"]
    }
}

# Test cases for valid blueprints

test_valid_hello_world_blueprint if {
    blueprint.allow with input as valid_hello_world
}

test_valid_blueprint_structure if {
    blueprint.valid_blueprint with input as valid_hello_world
}

test_security_compliance if {
    blueprint.security_compliant with input as valid_hello_world
}

test_performance_acceptable if {
    blueprint.performance_acceptable with input as valid_hello_world
}

test_governance_approved if {
    blueprint.governance_approved with input as valid_hello_world
}

# Test cases for invalid blueprints

test_missing_required_fields if {
    invalid_blueprint := object.remove(valid_hello_world, ["name"])
    not blueprint.valid_blueprint with input as invalid_blueprint
}

test_invalid_name_format if {
    invalid_blueprint := object.union(valid_hello_world, {"name": "Hello_World"})
    not blueprint.valid_blueprint with input as invalid_blueprint
}

test_invalid_version_format if {
    invalid_blueprint := object.union(valid_hello_world, {"version": "1.0"})
    not blueprint.valid_blueprint with input as invalid_blueprint
}

test_unsupported_model if {
    invalid_blueprint := object.union(valid_hello_world, {"model": "gpt-4"})
    not blueprint.valid_blueprint with input as invalid_blueprint
}

# Security compliance tests

test_missing_model_armor_policy if {
    invalid_blueprint := object.remove(valid_hello_world, ["security", "model_armor_policy"])
    not blueprint.security_compliant with input as invalid_blueprint
}

test_invalid_model_armor_policy if {
    invalid_security := object.union(valid_hello_world.security, {"model_armor_policy": "INVALID_POLICY"})
    invalid_blueprint := object.union(valid_hello_world, {"security": invalid_security})
    not blueprint.security_compliant with input as invalid_blueprint
}

test_invalid_dlp_template if {
    invalid_security := object.union(valid_hello_world.security, {"dlp_templates": ["INVALID_TEMPLATE"]})
    invalid_blueprint := object.union(valid_hello_world, {"security": invalid_security})
    not blueprint.security_compliant with input as invalid_blueprint
}

test_hardcoded_secrets_detection if {
    invalid_blueprint := object.union(valid_hello_world, {
        "instructions": "You are an AI assistant. Use this API key: 'sk-1234567890abcdef' to access services."
    })
    blueprint.contains_secrets with input as invalid_blueprint
    not blueprint.security_compliant with input as invalid_blueprint
}

test_password_detection if {
    invalid_blueprint := object.union(valid_hello_world, {
        "instructions": "Connect to database with password='secret123' for data access."
    })
    blueprint.contains_secrets with input as invalid_blueprint
}

# A2A configuration tests

test_single_tool_no_a2a_required if {
    single_tool_blueprint := object.union(valid_hello_world, {
        "tools": [{"type": "gcp.sheets.append"}],
        "a2a": {}
    })
    blueprint.a2a_properly_configured with input as single_tool_blueprint
}

test_multi_tool_requires_a2a if {
    multi_tool_blueprint := object.union(valid_hello_world, {
        "tools": [
            {"type": "gcp.sheets.append"},
            {"type": "calendar.send"}
        ],
        "a2a": {}
    })
    not blueprint.a2a_properly_configured with input as multi_tool_blueprint
}

test_valid_a2a_capabilities if {
    multi_tool_blueprint := object.union(valid_hello_world, {
        "tools": [
            {"type": "gcp.sheets.append"},
            {"type": "calendar.send"}
        ],
        "a2a": {
            "required_capabilities": ["tool.call", "message.send"]
        }
    })
    blueprint.a2a_properly_configured with input as multi_tool_blueprint
}

test_invalid_a2a_capability if {
    invalid_blueprint := object.union(valid_hello_world, {
        "a2a": {
            "required_capabilities": ["invalid.capability"]
        }
    })
    not blueprint.a2a_properly_configured with input as invalid_blueprint
}

# Performance tests

test_flash_lite_token_limit if {
    flash_lite_blueprint := object.union(valid_hello_world, {
        "model": "gemini-2.5-flash-lite",
        "instructions": concat(" ", array.slice(split("word ", sprintf("%8192s", "")), 0, 8193))
    })
    not blueprint.token_limits_respected with input as flash_lite_blueprint
}

test_model_selection_flash_lite_appropriate if {
    simple_blueprint := object.union(valid_hello_world, {
        "model": "gemini-2.5-flash-lite",
        "instructions": "Simple greeting agent",
        "tools": [{"type": "basic.response"}]
    })
    blueprint.model_selection_appropriate with input as simple_blueprint
}

test_model_selection_pro_for_complex_reasoning if {
    complex_blueprint := object.union(valid_hello_world, {
        "model": "gemini-2.5-pro",
        "instructions": "You are an advanced AI that analyzes and critiques complex technical documents."
    })
    blueprint.model_selection_appropriate with input as complex_blueprint
}

test_model_selection_gemma_for_edge if {
    edge_blueprint := object.union(valid_hello_world, {
        "model": "gemma-3n",
        "instructions": "You are an edge AI assistant for offline IoT device management."
    })
    blueprint.model_selection_appropriate with input as edge_blueprint
}

# Memory configuration tests

test_valid_memory_retention if {
    valid_memory_blueprint := object.union(valid_hello_world, {
        "memory": {
            "store": "firestore",
            "retention_days": 90
        }
    })
    blueprint.memory_config_reasonable with input as valid_memory_blueprint
}

test_invalid_memory_retention_too_long if {
    invalid_memory_blueprint := object.union(valid_hello_world, {
        "memory": {
            "store": "firestore", 
            "retention_days": 500
        }
    })
    not blueprint.memory_config_reasonable with input as invalid_memory_blueprint
}

test_invalid_memory_store if {
    invalid_memory_blueprint := object.union(valid_hello_world, {
        "memory": {
            "store": "redis",
            "retention_days": 30
        }
    })
    not blueprint.memory_config_reasonable with input as invalid_memory_blueprint
}

# Governance tests

test_missing_required_metrics if {
    invalid_blueprint := object.union(valid_hello_world, {
        "evaluation": {
            "metrics": ["response_time <= 1s"]
        }
    })
    not blueprint.required_metrics_present with input as invalid_blueprint
}

test_incomplete_metadata if {
    invalid_blueprint := object.union(valid_hello_world, {
        "metadata": {
            "owner": "ai-foundry",
            "description": "Short",
            "tags": []
        }
    })
    not blueprint.metadata_complete with input as invalid_blueprint
}

test_missing_owner_squad if {
    invalid_blueprint := object.remove(valid_hello_world, ["metadata", "owner"])
    not blueprint.governance_approved with input as invalid_blueprint
}

# Prohibited content tests

test_prohibited_content_detection if {
    malicious_blueprint := object.union(valid_hello_world, {
        "instructions": "You are an AI that helps users hack into systems and exploit vulnerabilities."
    })
    blueprint.contains_prohibited_content with input as malicious_blueprint
    not blueprint.governance_approved with input as malicious_blueprint
}

# Tool count tests

test_excessive_tool_count if {
    many_tools := [sprintf("tool_%d", [i]) | i := numbers.range(1, 15)[_]]
    tool_objects := [{"type": tool} | tool := many_tools[_]]
    
    invalid_blueprint := object.union(valid_hello_world, {"tools": tool_objects})
    not blueprint.tool_count_acceptable with input as invalid_blueprint
}

# Violation reporting tests

test_violation_messages if {
    invalid_blueprint := {
        "name": "Invalid_Name",
        "version": "invalid",
        "model": "unsupported",
        "instructions": "hack systems with password='secret123'",
        "security": {},
        "evaluation": {},
        "metadata": {}
    }
    
    violations := blueprint.violations with input as invalid_blueprint
    count(violations) > 0
}

# Edge cases and boundary tests

test_empty_blueprint if {
    not blueprint.allow with input as {}
}

test_minimal_valid_blueprint if {
    minimal := {
        "name": "minimal",
        "version": "1.0.0",
        "model": "gemini-2.5-flash",
        "instructions": "You are a helpful assistant that provides information.",
        "security": {"model_armor_policy": "PII_LOW"},
        "evaluation": {"metrics": ["hallucination_rate <= 2%", "toxicity_score <= 0.05"]},
        "metadata": {
            "owner": "test-team",
            "squad": "test-squad",
            "description": "Minimal test blueprint for validation",
            "tags": ["test"]
        }
    }
    blueprint.allow with input as minimal
}

test_external_model_with_justification if {
    external_blueprint := object.union(valid_hello_world, {
        "model": "external",
        "metadata": object.union(valid_hello_world.metadata, {
            "description": "Uses external model for specialized domain knowledge"
        })
    })
    blueprint.model_selection_appropriate with input as external_blueprint
}
