# Model Armor Policy for Metamorphic Foundry
# Implements content safety and security controls for agent interactions
# Integrates with Google Cloud Model Armor for real-time content filtering

package model_armor

import rego.v1

# Default decisions
default allow := false
default block := false
default log_only := false

# Main policy entry point
allow if {
    not block
    content_safe
    policy_compliant
}

block if {
    high_risk_content
}

block if {
    policy_violation
}

log_only if {
    low_risk_content
    not block
}

# Content safety checks
content_safe if {
    not contains_pii
    not contains_toxic_content
    not contains_harmful_instructions
    not contains_fraud_indicators
}

high_risk_content if {
    contains_pii
    input.armor_policy in high_security_policies
}

high_risk_content if {
    contains_toxic_content
    toxicity_score > high_toxicity_threshold
}

high_risk_content if {
    contains_harmful_instructions
}

high_risk_content if {
    contains_fraud_indicators
    input.armor_policy in fraud_protection_policies
}

low_risk_content if {
    contains_pii
    input.armor_policy in low_security_policies
}

low_risk_content if {
    contains_toxic_content
    toxicity_score <= low_toxicity_threshold
}

# Policy compliance
policy_compliant if {
    valid_armor_policy
    appropriate_policy_level
}

# PII detection patterns
contains_pii if {
    pii_patterns := [
        # Credit card numbers (simplified)
        "\\b(?:\\d{4}[\\s-]?){3}\\d{4}\\b",
        # Social Security Numbers
        "\\b\\d{3}-\\d{2}-\\d{4}\\b",
        # Email addresses
        "\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Z|a-z]{2,}\\b",
        # Phone numbers
        "\\b(?:\\+?1[\\s-]?)?\\(?\\d{3}\\)?[\\s-]?\\d{3}[\\s-]?\\d{4}\\b"
    ]
    
    some pattern in pii_patterns
    regex.match(pattern, input.content)
}

# Toxicity detection
contains_toxic_content if {
    toxic_keywords := [
        "hate", "violence", "threat", "harassment", "abuse",
        "discrimination", "offensive", "inappropriate", "vulgar"
    ]
    
    some keyword in toxic_keywords
    contains(lower(input.content), keyword)
}

# Estimated toxicity score (0.0 to 1.0)
toxicity_score := score if {
    toxic_indicators := [
        keyword | 
        keyword := ["hate", "violence", "threat", "harassment", "abuse"][_]
        contains(lower(input.content), keyword)
    ]
    
    # Simple scoring: 0.1 per toxic keyword, capped at 1.0
    raw_score := count(toxic_indicators) * 0.1
    score := min([raw_score, 1.0])
}

toxicity_score := 0.0 if {
    not contains_toxic_content
}

# Harmful instruction detection
contains_harmful_instructions if {
    harmful_patterns := [
        "how to hack",
        "create malware", 
        "bypass security",
        "illegal activities",
        "harmful substances",
        "violence against",
        "self-harm",
        "suicide"
    ]
    
    some pattern in harmful_patterns
    contains(lower(input.content), pattern)
}

# Fraud indicator detection
contains_fraud_indicators if {
    fraud_patterns := [
        "get rich quick",
        "guaranteed profit",
        "no risk investment",
        "urgent action required",
        "limited time offer",
        "wire money",
        "send payment",
        "verify account"
    ]
    
    some pattern in fraud_patterns
    contains(lower(input.content), pattern)
}

# Policy definitions
valid_armor_policy if {
    input.armor_policy in all_armor_policies
}

all_armor_policies := {
    "FRAUD_LOW", "FRAUD_MED", "FRAUD_HIGH",
    "PII_LOW", "PII_MED", "PII_HIGH"
}

high_security_policies := {"PII_HIGH", "FRAUD_HIGH"}
medium_security_policies := {"PII_MED", "FRAUD_MED"}
low_security_policies := {"PII_LOW", "FRAUD_LOW"}

fraud_protection_policies := {"FRAUD_LOW", "FRAUD_MED", "FRAUD_HIGH"}
pii_protection_policies := {"PII_LOW", "PII_MED", "PII_HIGH"}

# Thresholds
high_toxicity_threshold := 0.7
medium_toxicity_threshold := 0.4
low_toxicity_threshold := 0.2

# Policy level appropriateness
appropriate_policy_level if {
    # High-security contexts require high-security policies
    high_security_context
    input.armor_policy in high_security_policies
}

appropriate_policy_level if {
    # Medium-security contexts can use medium or high policies
    medium_security_context
    input.armor_policy in array.concat(medium_security_policies, high_security_policies)
}

appropriate_policy_level if {
    # Low-security contexts can use any policy
    low_security_context
    input.armor_policy in all_armor_policies
}

appropriate_policy_level if {
    # Default: any policy is acceptable if context not specified
    not input.security_context
}

# Security context determination
high_security_context if {
    input.security_context == "high"
}

high_security_context if {
    input.agent_type in high_security_agent_types
}

medium_security_context if {
    input.security_context == "medium"
}

medium_security_context if {
    input.agent_type in medium_security_agent_types
}

low_security_context if {
    input.security_context == "low"
}

low_security_context if {
    input.agent_type in low_security_agent_types
}

high_security_agent_types := {
    "financial-advisor",
    "healthcare-assistant", 
    "legal-counsel",
    "hr-assistant"
}

medium_security_agent_types := {
    "customer-service",
    "technical-support",
    "content-moderator"
}

low_security_agent_types := {
    "general-chat",
    "information-lookup",
    "weather-assistant"
}

# Policy violations
policy_violation if {
    # Content violates the specified policy level
    contains_pii
    input.armor_policy in pii_protection_policies
    pii_severity > policy_pii_threshold
}

policy_violation if {
    # Fraud content violates fraud protection policies
    contains_fraud_indicators
    input.armor_policy in fraud_protection_policies
    fraud_severity > policy_fraud_threshold
}

# Severity calculations
pii_severity := 0.3 if contains(input.content, "@")  # Email
pii_severity := 0.6 if regex.match("\\b\\d{3}-\\d{2}-\\d{4}\\b", input.content)  # SSN
pii_severity := 0.8 if regex.match("\\b(?:\\d{4}[\\s-]?){3}\\d{4}\\b", input.content)  # Credit card
pii_severity := 0.0 if not contains_pii

fraud_severity := count([
    pattern | 
    pattern := ["get rich quick", "guaranteed profit", "wire money"][_]
    contains(lower(input.content), pattern)
]) * 0.3

# Policy thresholds based on armor policy level
policy_pii_threshold := 0.2 if input.armor_policy == "PII_HIGH"
policy_pii_threshold := 0.5 if input.armor_policy == "PII_MED"
policy_pii_threshold := 0.8 if input.armor_policy == "PII_LOW"

policy_fraud_threshold := 0.2 if input.armor_policy == "FRAUD_HIGH"
policy_fraud_threshold := 0.5 if input.armor_policy == "FRAUD_MED"
policy_fraud_threshold := 0.8 if input.armor_policy == "FRAUD_LOW"

# Action recommendations
action := "BLOCK" if block
action := "ALLOW" if allow
action := "LOG_ONLY" if log_only
action := "REVIEW" if not allow; not block; not log_only

# Detailed verdict with reasoning
verdict := {
    "action": action,
    "policy": input.armor_policy,
    "toxicity_score": toxicity_score,
    "pii_detected": contains_pii,
    "pii_severity": pii_severity,
    "fraud_detected": contains_fraud_indicators,
    "fraud_severity": fraud_severity,
    "harmful_content": contains_harmful_instructions,
    "policy_compliant": policy_compliant,
    "reasoning": reasoning
}

reasoning contains "High toxicity content detected" if {
    toxicity_score > high_toxicity_threshold
}

reasoning contains "PII detected above policy threshold" if {
    contains_pii
    pii_severity > policy_pii_threshold
}

reasoning contains "Fraud indicators detected" if {
    contains_fraud_indicators
    fraud_severity > policy_fraud_threshold
}

reasoning contains "Harmful instructions detected" if {
    contains_harmful_instructions
}

reasoning contains "Content approved under current policy" if {
    allow
}

reasoning contains "Content flagged for review" if {
    not allow
    not block
}
