{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "https://metamorphic-foundry.dev/schemas/blueprint-0.4.2.json", "title": "Metamorphic Foundry Agent Blueprint Schema v0.4.2", "description": "Schema for declarative YAML blueprints that define AI agents for deployment in Vertex AI Applications Flow, ADK, and A2A protocol", "type": "object", "required": ["name", "version", "model", "instructions"], "additionalProperties": false, "properties": {"name": {"type": "string", "pattern": "^[a-z0-9]+(-[a-z0-9]+)*$", "description": "Kebab-case agent name; prepends image tags", "examples": ["meta-reflector", "hello-world", "data-processor"]}, "version": {"type": "string", "pattern": "^(0|[1-9]\\d*)\\.(0|[1-9]\\d*)\\.(0|[1-9]\\d*)(?:-((?:0|[1-9]\\d*|\\d*[a-zA-Z-][0-9a-zA-Z-]*)(?:\\.(?:0|[1-9]\\d*|\\d*[a-zA-Z-][0-9a-zA-Z-]*))*))?(?:\\+([0-9a-zA-Z-]+(?:\\.[0-9a-zA-Z-]+)*))?$", "description": "Semantic version; CI blocks non-semver pushes", "examples": ["0.1.0", "1.2.3", "2.0.0-beta.1"]}, "model": {"type": "string", "enum": ["gemini-2.5-pro", "gemini-2.5-flash", "gemini-2.5-flash-lite", "gemma-3n", "external"], "description": "Target model for agent execution"}, "instructions": {"type": "string", "maxLength": 8192, "description": "System prompt; 8k-token max for Flash-Lite compatibility", "examples": ["You are Meta-Reflector, tasked with critiquing design docs..."]}, "tools": {"type": "array", "description": "Array of tool specifications mapping to ADK ToolSpec", "items": {"type": "object", "required": ["type"], "properties": {"type": {"type": "string", "description": "Tool type identifier", "examples": ["gcp.sheets.append", "calendar.send", "code.search"]}, "auth": {"type": "string", "enum": ["service_account", "oauth2", "api_key"], "description": "Authentication method for the tool"}, "config": {"type": "object", "description": "Tool-specific configuration parameters"}}, "additionalProperties": true}}, "memory": {"type": "object", "description": "Memory and retrieval configuration", "properties": {"store": {"type": "string", "enum": ["firestore", "cloud-sql-postgres", "sqlite"], "description": "Vector store backend"}, "embedding_model": {"type": "string", "default": "text-embedding-gecko@001", "description": "Embedding model for vector storage"}, "retention_days": {"type": "integer", "minimum": 1, "maximum": 400, "default": 30, "description": "Memory retention period in days"}}, "additionalProperties": false}, "evaluation": {"type": "object", "required": ["metrics"], "description": "Evaluation metrics and thresholds", "properties": {"metrics": {"type": "array", "minItems": 1, "items": {"type": "string", "description": "Metric specification with threshold", "examples": ["hallucination_rate <= 2%", "toxicity_score <= 0.05", "response_latency_ms <= 1000"]}}}, "additionalProperties": false}, "security": {"type": "object", "required": ["model_armor_policy"], "description": "Security and content safety configuration", "properties": {"model_armor_policy": {"type": "string", "enum": ["FRAUD_LOW", "FRAUD_MED", "FRAUD_HIGH", "PII_LOW", "PII_MED", "PII_HIGH"], "description": "Model Armor content safety policy level"}, "dlp_templates": {"type": "array", "items": {"type": "string"}, "description": "Data Loss Prevention template identifiers"}}, "additionalProperties": false}, "a2a": {"type": "object", "description": "Agent-to-Agent protocol configuration", "properties": {"required_capabilities": {"type": "array", "items": {"type": "string", "enum": ["memory.put", "memory.get", "tool.call", "message.send", "message.receive"]}, "description": "Required A2A capabilities for secure messaging"}, "token_binding": {"type": "boolean", "default": true, "description": "Enable token-bound mTLS handshake"}}, "additionalProperties": false}, "metadata": {"type": "object", "description": "Agent metadata for tracking and organization", "properties": {"owner": {"type": "string", "description": "Team or individual responsible for the agent"}, "squad": {"type": "string", "description": "Squad or team identifier"}, "ticket": {"type": "string", "description": "Associated ticket or issue identifier"}, "description": {"type": "string", "description": "Human-readable description of agent purpose"}, "tags": {"type": "array", "items": {"type": "string"}, "description": "Classification tags for the agent"}}, "additionalProperties": true}}, "$defs": {"semver": {"type": "string", "pattern": "^(0|[1-9]\\d*)\\.(0|[1-9]\\d*)\\.(0|[1-9]\\d*)(?:-((?:0|[1-9]\\d*|\\d*[a-zA-Z-][0-9a-zA-Z-]*)(?:\\.(?:0|[1-9]\\d*|\\d*[a-zA-Z-][0-9a-zA-Z-]*))*))?(?:\\+([0-9a-zA-Z-]+(?:\\.[0-9a-zA-Z-]+)*))?$"}, "kebab_case": {"type": "string", "pattern": "^[a-z0-9]+(-[a-z0-9]+)*$"}}}