#!/usr/bin/env python3
"""
Agent Runner for Metamorphic Foundry
Executes agent blueprints in containerized environments with observability.
"""

import os
import sys
import yaml
import json
import time
import logging
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime, timezone
import uuid

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('agent-runner')


class AgentRunner:
    """Runs agents based on blueprint specifications."""
    
    def __init__(self, blueprint_path: str):
        self.blueprint_path = Path(blueprint_path)
        self.blueprint = self._load_blueprint()
        self.agent_id = str(uuid.uuid4())
        self.start_time = datetime.now(timezone.utc)
        
    def _load_blueprint(self) -> Dict[str, Any]:
        """Load and validate blueprint YAML."""
        try:
            with open(self.blueprint_path, 'r') as f:
                blueprint = yaml.safe_load(f)
            
            # Basic validation
            required_fields = ['name', 'version', 'model', 'instructions']
            for field in required_fields:
                if field not in blueprint:
                    raise ValueError(f"Missing required field: {field}")
            
            logger.info(f"Loaded blueprint: {blueprint['name']} v{blueprint['version']}")
            return blueprint
            
        except Exception as e:
            logger.error(f"Failed to load blueprint: {e}")
            raise
    
    def _setup_observability(self):
        """Initialize observability and telemetry."""
        # Log agent startup to BigQuery-compatible format
        startup_event = {
            'agent_id': self.agent_id,
            'agent_name': self.blueprint['name'],
            'agent_version': self.blueprint['version'],
            'model': self.blueprint['model'],
            'environment': os.getenv('ENVIRONMENT', 'unknown'),
            'timestamp': self.start_time.isoformat(),
            'event_type': 'agent_startup',
            'metadata': self.blueprint.get('metadata', {})
        }
        
        # In production, this would send to BigQuery via Pub/Sub
        logger.info(f"Agent startup event: {json.dumps(startup_event)}")
        
    def _health_check(self) -> Dict[str, Any]:
        """Provide health check endpoint response."""
        uptime = (datetime.now(timezone.utc) - self.start_time).total_seconds()
        
        return {
            'status': 'healthy',
            'agent_id': self.agent_id,
            'agent_name': self.blueprint['name'],
            'version': self.blueprint['version'],
            'model': self.blueprint['model'],
            'uptime_seconds': uptime,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
    
    def _process_request(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process an agent request."""
        request_id = str(uuid.uuid4())
        start_time = time.time()
        
        try:
            # Extract request details
            user_message = request_data.get('message', '')
            context = request_data.get('context', {})
            
            # Simulate agent processing based on model type
            model = self.blueprint['model']
            instructions = self.blueprint['instructions']
            
            # Mock response generation (in production, this would call Vertex AI)
            if model.startswith('gemini-2.5-flash'):
                # Fast response simulation
                time.sleep(0.1)
                response = f"Hello! I'm {self.blueprint['name']}, running on {model}. You said: {user_message}"
            elif model.startswith('gemini-2.5-pro'):
                # Slower, more detailed response simulation
                time.sleep(0.5)
                response = f"Greetings! I'm {self.blueprint['name']}, an advanced agent running on {model}. I've analyzed your message: '{user_message}' and can provide detailed assistance based on my instructions: {instructions[:100]}..."
            else:
                response = f"I'm {self.blueprint['name']} running on {model}. Message received: {user_message}"
            
            processing_time = time.time() - start_time
            
            # Log turn data for BigQuery
            turn_data = {
                'agent_id': self.agent_id,
                'request_id': request_id,
                'agent_name': self.blueprint['name'],
                'model': model,
                'user_message_hash': hash(user_message),  # Don't log actual content
                'response_hash': hash(response),
                'processing_time_ms': processing_time * 1000,
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'token_count': len(user_message.split()) + len(response.split()),  # Rough estimate
                'evaluation_metrics': {
                    'hallucination_rate': 0.01,  # Mock metric
                    'toxicity_score': 0.02,      # Mock metric
                    'user_satisfaction': 4.2     # Mock metric
                }
            }
            
            logger.info(f"Agent turn completed: {json.dumps(turn_data)}")
            
            return {
                'request_id': request_id,
                'response': response,
                'processing_time_ms': processing_time * 1000,
                'model': model,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error processing request {request_id}: {e}")
            return {
                'request_id': request_id,
                'error': str(e),
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
    
    def run_server(self, port: int = 8080):
        """Run a simple HTTP server for the agent."""
        from http.server import HTTPServer, BaseHTTPRequestHandler
        import urllib.parse
        
        class AgentHandler(BaseHTTPRequestHandler):
            def do_GET(self):
                if self.path == '/health':
                    self.send_response(200)
                    self.send_header('Content-type', 'application/json')
                    self.end_headers()
                    health_data = runner._health_check()
                    self.wfile.write(json.dumps(health_data).encode())
                else:
                    self.send_response(404)
                    self.end_headers()
            
            def do_POST(self):
                if self.path == '/chat':
                    content_length = int(self.headers['Content-Length'])
                    post_data = self.rfile.read(content_length)
                    
                    try:
                        request_data = json.loads(post_data.decode('utf-8'))
                        response_data = runner._process_request(request_data)
                        
                        self.send_response(200)
                        self.send_header('Content-type', 'application/json')
                        self.end_headers()
                        self.wfile.write(json.dumps(response_data).encode())
                    except Exception as e:
                        self.send_response(500)
                        self.send_header('Content-type', 'application/json')
                        self.end_headers()
                        error_response = {'error': str(e)}
                        self.wfile.write(json.dumps(error_response).encode())
                else:
                    self.send_response(404)
                    self.end_headers()
            
            def log_message(self, format, *args):
                # Suppress default HTTP server logging
                pass
        
        runner = self
        self._setup_observability()
        
        server = HTTPServer(('0.0.0.0', port), AgentHandler)
        logger.info(f"Starting agent server on port {port}")
        logger.info(f"Agent: {self.blueprint['name']} v{self.blueprint['version']}")
        logger.info(f"Model: {self.blueprint['model']}")
        
        try:
            server.serve_forever()
        except KeyboardInterrupt:
            logger.info("Shutting down agent server")
            server.shutdown()


def main():
    """Main entry point."""
    if len(sys.argv) != 2:
        print("Usage: agent_runner.py <blueprint.yaml>")
        sys.exit(1)
    
    blueprint_path = sys.argv[1]
    
    if not Path(blueprint_path).exists():
        print(f"Error: Blueprint file not found: {blueprint_path}")
        sys.exit(1)
    
    try:
        runner = AgentRunner(blueprint_path)
        runner.run_server()
    except Exception as e:
        logger.error(f"Failed to start agent: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
