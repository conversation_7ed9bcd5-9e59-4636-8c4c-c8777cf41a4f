#!/bin/bash

# Metamorphic Foundry Dashboard Deployment Script
# Deploys Looker Studio dashboards to Google Cloud Monitoring
# Usage: ./deploy_dashboards.sh <PROJECT_ID> [ENVIRONMENT]

set -euo pipefail

# Configuration
PROJECT_ID="${1:-}"
ENVIRONMENT="${2:-production}"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOOKER_DIR="${SCRIPT_DIR}/../looker"
LOG_FILE="/tmp/dashboard_deployment_$(date +%Y%m%d_%H%M%S).log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${1}" | tee -a "${LOG_FILE}"
}

# Error handling
error_exit() {
    log "${RED}ERROR: ${1}${NC}"
    exit 1
}

# Success message
success() {
    log "${GREEN}SUCCESS: ${1}${NC}"
}

# Warning message
warning() {
    log "${YELLOW}WARNING: ${1}${NC}"
}

# Info message
info() {
    log "${BLUE}INFO: ${1}${NC}"
}

# Validate prerequisites
validate_prerequisites() {
    info "Validating prerequisites..."
    
    # Check if PROJECT_ID is provided
    if [[ -z "${PROJECT_ID}" ]]; then
        error_exit "PROJECT_ID is required. Usage: $0 <PROJECT_ID> [ENVIRONMENT]"
    fi
    
    # Check if gcloud is installed and authenticated
    if ! command -v gcloud &> /dev/null; then
        error_exit "gcloud CLI is not installed. Please install it first."
    fi
    
    # Check if user is authenticated
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -n1 &> /dev/null; then
        error_exit "Not authenticated with gcloud. Run 'gcloud auth login' first."
    fi
    
    # Check if project exists and user has access
    if ! gcloud projects describe "${PROJECT_ID}" &> /dev/null; then
        error_exit "Project '${PROJECT_ID}' not found or no access."
    fi
    
    # Check if Monitoring API is enabled
    if ! gcloud services list --enabled --project="${PROJECT_ID}" --filter="name:monitoring.googleapis.com" --format="value(name)" | grep -q monitoring; then
        warning "Monitoring API not enabled. Enabling now..."
        gcloud services enable monitoring.googleapis.com --project="${PROJECT_ID}"
    fi
    
    # Check if dashboard files exist
    if [[ ! -d "${LOOKER_DIR}" ]]; then
        error_exit "Looker dashboard directory not found: ${LOOKER_DIR}"
    fi
    
    success "Prerequisites validated"
}

# Replace placeholders in dashboard JSON
process_dashboard_template() {
    local input_file="$1"
    local output_file="$2"
    
    info "Processing template: $(basename "${input_file}")"
    
    # Replace PROJECT_ID placeholder
    sed "s/{PROJECT_ID}/${PROJECT_ID}/g" "${input_file}" > "${output_file}"
    
    # Validate JSON syntax
    if ! python3 -m json.tool "${output_file}" > /dev/null 2>&1; then
        error_exit "Invalid JSON in processed file: ${output_file}"
    fi
    
    success "Template processed: $(basename "${output_file}")"
}

# Deploy single dashboard
deploy_dashboard() {
    local dashboard_file="$1"
    local dashboard_name="$(basename "${dashboard_file}" .json)"
    
    info "Deploying dashboard: ${dashboard_name}"
    
    # Create temporary processed file
    local temp_file="/tmp/${dashboard_name}_processed.json"
    process_dashboard_template "${dashboard_file}" "${temp_file}"
    
    # Deploy using gcloud monitoring dashboards create
    local dashboard_id
    dashboard_id=$(gcloud monitoring dashboards create \
        --config-from-file="${temp_file}" \
        --project="${PROJECT_ID}" \
        --format="value(name)" 2>>"${LOG_FILE}")
    
    if [[ $? -eq 0 ]]; then
        success "Dashboard deployed: ${dashboard_name}"
        info "Dashboard ID: ${dashboard_id}"
        info "Dashboard URL: https://console.cloud.google.com/monitoring/dashboards/custom/${dashboard_id##*/}?project=${PROJECT_ID}"
        
        # Store dashboard info for later reference
        echo "${dashboard_name},${dashboard_id},$(date -Iseconds)" >> "/tmp/deployed_dashboards_${PROJECT_ID}.csv"
    else
        error_exit "Failed to deploy dashboard: ${dashboard_name}"
    fi
    
    # Cleanup temporary file
    rm -f "${temp_file}"
}

# Update existing dashboard
update_dashboard() {
    local dashboard_file="$1"
    local dashboard_id="$2"
    local dashboard_name="$(basename "${dashboard_file}" .json)"
    
    info "Updating dashboard: ${dashboard_name} (ID: ${dashboard_id})"
    
    # Create temporary processed file
    local temp_file="/tmp/${dashboard_name}_processed.json"
    process_dashboard_template "${dashboard_file}" "${temp_file}"
    
    # Update using gcloud monitoring dashboards update
    if gcloud monitoring dashboards update "${dashboard_id}" \
        --config-from-file="${temp_file}" \
        --project="${PROJECT_ID}" &>>"${LOG_FILE}"; then
        success "Dashboard updated: ${dashboard_name}"
    else
        error_exit "Failed to update dashboard: ${dashboard_name}"
    fi
    
    # Cleanup temporary file
    rm -f "${temp_file}"
}

# Check if dashboard already exists
dashboard_exists() {
    local dashboard_name="$1"
    
    # List existing dashboards and check for matching display name
    gcloud monitoring dashboards list \
        --project="${PROJECT_ID}" \
        --format="value(displayName,name)" \
        --filter="displayName:\"Metamorphic Foundry - ${dashboard_name}\"" \
        2>/dev/null | head -n1
}

# Deploy all dashboards
deploy_all_dashboards() {
    info "Starting dashboard deployment for project: ${PROJECT_ID}"
    info "Environment: ${ENVIRONMENT}"
    info "Log file: ${LOG_FILE}"
    
    # Initialize deployment tracking
    echo "dashboard_name,dashboard_id,deployed_at" > "/tmp/deployed_dashboards_${PROJECT_ID}.csv"
    
    # Dashboard mapping
    declare -A dashboards=(
        ["Executive Overview"]="executive_overview_dashboard.json"
        ["Latency & Performance"]="latency_performance_dashboard.json"
        ["Policy & Compliance"]="policy_compliance_dashboard.json"
        ["Deployment Analytics"]="deployment_analytics_dashboard.json"
    )
    
    local deployed_count=0
    local updated_count=0
    local failed_count=0
    
    for dashboard_name in "${!dashboards[@]}"; do
        local dashboard_file="${LOOKER_DIR}/${dashboards[$dashboard_name]}"
        
        if [[ ! -f "${dashboard_file}" ]]; then
            warning "Dashboard file not found: ${dashboard_file}"
            ((failed_count++))
            continue
        fi
        
        # Check if dashboard already exists
        local existing_dashboard
        existing_dashboard=$(dashboard_exists "${dashboard_name}")
        
        if [[ -n "${existing_dashboard}" ]]; then
            # Extract dashboard ID from the result
            local dashboard_id
            dashboard_id=$(echo "${existing_dashboard}" | cut -d' ' -f2)
            
            info "Dashboard '${dashboard_name}' already exists. Updating..."
            if update_dashboard "${dashboard_file}" "${dashboard_id}"; then
                ((updated_count++))
            else
                ((failed_count++))
            fi
        else
            info "Deploying new dashboard: ${dashboard_name}"
            if deploy_dashboard "${dashboard_file}"; then
                ((deployed_count++))
            else
                ((failed_count++))
            fi
        fi
    done
    
    # Summary
    info "Deployment Summary:"
    info "  New dashboards deployed: ${deployed_count}"
    info "  Existing dashboards updated: ${updated_count}"
    info "  Failed deployments: ${failed_count}"
    info "  Total dashboards processed: $((deployed_count + updated_count + failed_count))"
    
    if [[ ${failed_count} -eq 0 ]]; then
        success "All dashboards deployed successfully!"
    else
        warning "Some dashboards failed to deploy. Check log: ${LOG_FILE}"
    fi
    
    # Display dashboard URLs
    info "Dashboard URLs:"
    while IFS=',' read -r name id timestamp; do
        if [[ "${name}" != "dashboard_name" ]]; then
            local dashboard_id_short="${id##*/}"
            info "  ${name}: https://console.cloud.google.com/monitoring/dashboards/custom/${dashboard_id_short}?project=${PROJECT_ID}"
        fi
    done < "/tmp/deployed_dashboards_${PROJECT_ID}.csv"
}

# Cleanup function
cleanup() {
    info "Cleaning up temporary files..."
    rm -f /tmp/*_processed.json
    rm -f "/tmp/deployed_dashboards_${PROJECT_ID}.csv"
}

# Set up cleanup trap
trap cleanup EXIT

# Main execution
main() {
    info "Metamorphic Foundry Dashboard Deployment"
    info "========================================"
    
    validate_prerequisites
    deploy_all_dashboards
    
    success "Dashboard deployment completed!"
    info "Log file saved: ${LOG_FILE}"
}

# Run main function
main "$@"
