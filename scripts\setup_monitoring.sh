#!/bin/bash

# Metamorphic Foundry Monitoring Setup Script
# Configures comprehensive monitoring, alerting, and SLO tracking
# Usage: ./setup_monitoring.sh <PROJECT_ID> [ENVIRONMENT]

set -euo pipefail

# Configuration
PROJECT_ID="${1:-}"
ENVIRONMENT="${2:-production}"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_FILE="/tmp/monitoring_setup_$(date +%Y%m%d_%H%M%S).log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${1}" | tee -a "${LOG_FILE}"
}

# Error handling
error_exit() {
    log "${RED}ERROR: ${1}${NC}"
    exit 1
}

# Success message
success() {
    log "${GREEN}SUCCESS: ${1}${NC}"
}

# Warning message
warning() {
    log "${YELLOW}WARNING: ${1}${NC}"
}

# Info message
info() {
    log "${BLUE}INFO: ${1}${NC}"
}

# Validate prerequisites
validate_prerequisites() {
    info "Validating prerequisites..."
    
    # Check if PROJECT_ID is provided
    if [[ -z "${PROJECT_ID}" ]]; then
        error_exit "PROJECT_ID is required. Usage: $0 <PROJECT_ID> [ENVIRONMENT]"
    fi
    
    # Check if gcloud is installed and authenticated
    if ! command -v gcloud &> /dev/null; then
        error_exit "gcloud CLI is not installed. Please install it first."
    fi
    
    # Check if user is authenticated
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -n1 &> /dev/null; then
        error_exit "Not authenticated with gcloud. Run 'gcloud auth login' first."
    fi
    
    # Check if project exists and user has access
    if ! gcloud projects describe "${PROJECT_ID}" &> /dev/null; then
        error_exit "Project '${PROJECT_ID}' not found or no access."
    fi
    
    # Check if required APIs are enabled
    local required_apis=(
        "monitoring.googleapis.com"
        "logging.googleapis.com"
        "bigquery.googleapis.com"
        "pubsub.googleapis.com"
    )
    
    for api in "${required_apis[@]}"; do
        if ! gcloud services list --enabled --project="${PROJECT_ID}" --filter="name:${api}" --format="value(name)" | grep -q "${api}"; then
            warning "API ${api} not enabled. Enabling now..."
            gcloud services enable "${api}" --project="${PROJECT_ID}"
        fi
    done
    
    success "Prerequisites validated"
}

# Create custom metrics
create_custom_metrics() {
    info "Creating custom metrics..."
    
    # Agent turn latency metric
    if ! gcloud logging metrics describe foundry_agent_turn_latency --project="${PROJECT_ID}" &>/dev/null; then
        gcloud logging metrics create foundry_agent_turn_latency \
            --description="Agent turn processing latency" \
            --log-filter='resource.type="bigquery_dataset" AND jsonPayload.processing_time_ms>0' \
            --value-extractor='EXTRACT(jsonPayload.processing_time_ms)' \
            --label-extractors='agent_name=EXTRACT(jsonPayload.agent_name),model=EXTRACT(jsonPayload.model)' \
            --project="${PROJECT_ID}"
        success "Created agent turn latency metric"
    else
        info "Agent turn latency metric already exists"
    fi
    
    # Hallucination rate metric
    if ! gcloud logging metrics describe foundry_hallucination_rate --project="${PROJECT_ID}" &>/dev/null; then
        gcloud logging metrics create foundry_hallucination_rate \
            --description="Agent hallucination rate" \
            --log-filter='resource.type="bigquery_dataset" AND jsonPayload.hallucination_rate>=0' \
            --value-extractor='EXTRACT(jsonPayload.hallucination_rate)' \
            --label-extractors='agent_name=EXTRACT(jsonPayload.agent_name)' \
            --project="${PROJECT_ID}"
        success "Created hallucination rate metric"
    else
        info "Hallucination rate metric already exists"
    fi
    
    # Toxicity score metric
    if ! gcloud logging metrics describe foundry_toxicity_score --project="${PROJECT_ID}" &>/dev/null; then
        gcloud logging metrics create foundry_toxicity_score \
            --description="Agent toxicity score" \
            --log-filter='resource.type="bigquery_dataset" AND jsonPayload.toxicity_score>=0' \
            --value-extractor='EXTRACT(jsonPayload.toxicity_score)' \
            --label-extractors='agent_name=EXTRACT(jsonPayload.agent_name)' \
            --project="${PROJECT_ID}"
        success "Created toxicity score metric"
    else
        info "Toxicity score metric already exists"
    fi
    
    # Cost tracking metric
    if ! gcloud logging metrics describe foundry_cost_tracking --project="${PROJECT_ID}" &>/dev/null; then
        gcloud logging metrics create foundry_cost_tracking \
            --description="Agent cost tracking" \
            --log-filter='resource.type="bigquery_dataset" AND jsonPayload.cost_usd>0' \
            --value-extractor='EXTRACT(jsonPayload.cost_usd)' \
            --label-extractors='agent_name=EXTRACT(jsonPayload.agent_name),model=EXTRACT(jsonPayload.model)' \
            --project="${PROJECT_ID}"
        success "Created cost tracking metric"
    else
        info "Cost tracking metric already exists"
    fi
}

# Create notification channels
create_notification_channels() {
    info "Creating notification channels..."
    
    # Email notification channel
    local email_channel_id
    email_channel_id=$(gcloud alpha monitoring channels list \
        --filter='displayName="Foundry Email Alerts"' \
        --format='value(name)' \
        --project="${PROJECT_ID}" | head -n1)
    
    if [[ -z "${email_channel_id}" ]]; then
        # Create email channel (requires manual configuration)
        warning "Email notification channel needs to be created manually in the Cloud Console"
        info "Go to: https://console.cloud.google.com/monitoring/alerting/notifications?project=${PROJECT_ID}"
    else
        success "Email notification channel exists: ${email_channel_id}"
    fi
    
    # Slack notification channel (if webhook URL provided)
    if [[ -n "${SLACK_WEBHOOK_URL:-}" ]]; then
        local slack_channel_id
        slack_channel_id=$(gcloud alpha monitoring channels list \
            --filter='displayName="Foundry Slack Alerts"' \
            --format='value(name)' \
            --project="${PROJECT_ID}" | head -n1)
        
        if [[ -z "${slack_channel_id}" ]]; then
            warning "Slack notification channel needs to be created manually"
            info "Configure Slack webhook in Cloud Console"
        else
            success "Slack notification channel exists: ${slack_channel_id}"
        fi
    fi
}

# Create uptime checks
create_uptime_checks() {
    info "Creating uptime checks..."
    
    # Check if BigQuery dataset is accessible
    if ! gcloud alpha monitoring uptime list \
        --filter='displayName="Foundry BigQuery Health"' \
        --project="${PROJECT_ID}" &>/dev/null; then
        
        warning "Uptime checks need to be configured manually for your specific endpoints"
        info "Configure uptime checks in Cloud Console for:"
        info "  - Agent API endpoints"
        info "  - BigQuery dataset accessibility"
        info "  - Pub/Sub topic health"
    fi
}

# Create alert policies
create_alert_policies() {
    info "Creating alert policies..."
    
    # High error rate alert
    local error_rate_policy
    error_rate_policy=$(gcloud alpha monitoring policies list \
        --filter='displayName="Foundry High Error Rate"' \
        --format='value(name)' \
        --project="${PROJECT_ID}" | head -n1)
    
    if [[ -z "${error_rate_policy}" ]]; then
        warning "Alert policies need to be created via Terraform or manually"
        info "Run: terraform apply to create comprehensive alert policies"
    else
        success "Error rate alert policy exists: ${error_rate_policy}"
    fi
}

# Setup log-based metrics
setup_log_metrics() {
    info "Setting up log-based metrics..."
    
    # Create log sink for BigQuery
    local sink_name="foundry-observability-sink"
    if ! gcloud logging sinks describe "${sink_name}" --project="${PROJECT_ID}" &>/dev/null; then
        gcloud logging sinks create "${sink_name}" \
            "bigquery.googleapis.com/projects/${PROJECT_ID}/datasets/foundry_observability" \
            --log-filter='resource.type="bigquery_dataset" OR resource.type="pubsub_topic"' \
            --project="${PROJECT_ID}"
        success "Created log sink: ${sink_name}"
    else
        info "Log sink already exists: ${sink_name}"
    fi
    
    # Grant BigQuery Data Editor role to the sink's service account
    local sink_sa
    sink_sa=$(gcloud logging sinks describe "${sink_name}" \
        --project="${PROJECT_ID}" \
        --format='value(writerIdentity)')
    
    if [[ -n "${sink_sa}" ]]; then
        gcloud projects add-iam-policy-binding "${PROJECT_ID}" \
            --member="${sink_sa}" \
            --role="roles/bigquery.dataEditor" \
            --quiet
        success "Granted BigQuery permissions to sink service account"
    fi
}

# Verify monitoring setup
verify_monitoring() {
    info "Verifying monitoring setup..."
    
    # Check if metrics are being collected
    local metrics_count
    metrics_count=$(gcloud logging metrics list --project="${PROJECT_ID}" --format='value(name)' | grep -c "foundry" || true)
    
    if [[ ${metrics_count} -gt 0 ]]; then
        success "Found ${metrics_count} Foundry custom metrics"
    else
        warning "No Foundry custom metrics found"
    fi
    
    # Check notification channels
    local channels_count
    channels_count=$(gcloud alpha monitoring channels list --project="${PROJECT_ID}" --format='value(name)' | wc -l || true)
    
    if [[ ${channels_count} -gt 0 ]]; then
        success "Found ${channels_count} notification channels"
    else
        warning "No notification channels configured"
    fi
    
    # Check if BigQuery dataset exists
    if gcloud bq ls --project_id="${PROJECT_ID}" | grep -q "foundry_observability"; then
        success "BigQuery observability dataset exists"
    else
        warning "BigQuery observability dataset not found"
    fi
}

# Generate monitoring configuration summary
generate_summary() {
    info "Generating monitoring configuration summary..."
    
    local summary_file="/tmp/foundry_monitoring_summary_${PROJECT_ID}.md"
    
    cat > "${summary_file}" << EOF
# Metamorphic Foundry Monitoring Summary

**Project:** ${PROJECT_ID}
**Environment:** ${ENVIRONMENT}
**Setup Date:** $(date -Iseconds)

## Custom Metrics Created
- \`foundry_agent_turn_latency\` - Agent processing latency
- \`foundry_hallucination_rate\` - Hallucination rate tracking
- \`foundry_toxicity_score\` - Toxicity score monitoring
- \`foundry_cost_tracking\` - Cost per agent turn

## SLO Targets
- **Hallucination Rate:** ≤ 2% (98% of requests)
- **Toxicity Score:** ≤ 0.05 (95% of requests)
- **Latency (Flash):** < 1s P95 (95% of requests)
- **Latency (Pro):** < 8s P95 (95% of requests)
- **Availability:** 99.9% uptime

## Alert Policies
- Critical: SLO burn rate alerts
- Warning: Latency threshold alerts
- Info: Cost anomaly detection

## Dashboards
- Executive Overview: High-level KPIs
- Performance: Latency and throughput metrics
- Compliance: Policy violations and quality scores
- Deployment: CI/CD pipeline health

## Next Steps
1. Configure notification channels in Cloud Console
2. Set up Slack/PagerDuty integrations
3. Deploy Looker dashboards
4. Test alert policies with synthetic data
5. Set up automated SLO reporting

## Useful Commands
\`\`\`bash
# View custom metrics
gcloud logging metrics list --project=${PROJECT_ID}

# Check SLO status
gcloud alpha monitoring slos list --project=${PROJECT_ID}

# View alert policies
gcloud alpha monitoring policies list --project=${PROJECT_ID}

# Check notification channels
gcloud alpha monitoring channels list --project=${PROJECT_ID}
\`\`\`

## Monitoring URLs
- [Cloud Monitoring](https://console.cloud.google.com/monitoring?project=${PROJECT_ID})
- [BigQuery Observability](https://console.cloud.google.com/bigquery?project=${PROJECT_ID}&ws=!1m4!1m3!3m2!1s${PROJECT_ID}!2sfoundry_observability)
- [Pub/Sub Topics](https://console.cloud.google.com/cloudpubsub/topic/list?project=${PROJECT_ID})
- [Log Explorer](https://console.cloud.google.com/logs/query?project=${PROJECT_ID})
EOF
    
    success "Monitoring summary saved to: ${summary_file}"
    info "Review the summary for next steps and configuration details"
}

# Main execution
main() {
    info "Metamorphic Foundry Monitoring Setup"
    info "====================================="
    info "Project: ${PROJECT_ID}"
    info "Environment: ${ENVIRONMENT}"
    info "Log file: ${LOG_FILE}"
    
    validate_prerequisites
    create_custom_metrics
    create_notification_channels
    create_uptime_checks
    create_alert_policies
    setup_log_metrics
    verify_monitoring
    generate_summary
    
    success "Monitoring setup completed!"
    info "Next steps:"
    info "1. Review the monitoring summary"
    info "2. Configure notification channels manually"
    info "3. Deploy Terraform monitoring resources"
    info "4. Test alert policies"
    info "5. Set up Looker dashboards"
    
    warning "Note: Some resources require manual configuration in Cloud Console"
    info "See the generated summary for detailed instructions"
}

# Run main function
main "$@"
