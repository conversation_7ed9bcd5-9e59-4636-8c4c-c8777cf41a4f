#!/usr/bin/env python3
"""
Blueprint validation script for Metamorphic Foundry pre-commit hooks.
Validates YAML blueprints against the JSONSchema specification.
"""

import json
import sys
import yaml
from pathlib import Path
from typing import List, Dict, Any
import jsonschema
from jsonschema import validate, ValidationError


def load_schema() -> Dict[str, Any]:
    """Load the blueprint JSONSchema from schemas directory."""
    schema_path = Path("schemas/blueprint-0.4.2.json")
    if not schema_path.exists():
        print(f"Error: Schema file not found at {schema_path}")
        sys.exit(1)
    
    with open(schema_path, 'r') as f:
        return json.load(f)


def load_blueprint(file_path: Path) -> Dict[str, Any]:
    """Load and parse a YAML blueprint file."""
    try:
        with open(file_path, 'r') as f:
            return yaml.safe_load(f)
    except yaml.YAMLError as e:
        print(f"Error parsing <PERSON>AM<PERSON> in {file_path}: {e}")
        return None
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        return None


def validate_blueprint(blueprint: Dict[str, Any], schema: Dict[str, Any], file_path: Path) -> bool:
    """Validate a blueprint against the schema."""
    try:
        validate(instance=blueprint, schema=schema)
        print(f"✅ {file_path}: Valid blueprint")
        return True
    except ValidationError as e:
        print(f"❌ {file_path}: Validation failed")
        print(f"   Error: {e.message}")
        if e.absolute_path:
            print(f"   Path: {' -> '.join(str(p) for p in e.absolute_path)}")
        return False
    except Exception as e:
        print(f"❌ {file_path}: Unexpected error during validation: {e}")
        return False


def validate_semantic_rules(blueprint: Dict[str, Any], file_path: Path) -> bool:
    """Validate semantic rules beyond JSONSchema."""
    errors = []
    
    # Check model-specific constraints
    model = blueprint.get('model')
    instructions = blueprint.get('instructions', '')
    
    if model in ['gemini-2.5-flash-lite'] and len(instructions) > 8192:
        errors.append("Flash-Lite model requires instructions ≤ 8k tokens")
    
    # Check required evaluation metrics
    evaluation = blueprint.get('evaluation', {})
    metrics = evaluation.get('metrics', [])
    
    required_metrics = ['hallucination_rate', 'toxicity_score']
    for metric in required_metrics:
        if not any(metric in m for m in metrics):
            errors.append(f"Missing required evaluation metric: {metric}")
    
    # Check security policy is set
    security = blueprint.get('security', {})
    if not security.get('model_armor_policy'):
        errors.append("Missing required security.model_armor_policy")
    
    # Check A2A capabilities for multi-agent scenarios
    a2a = blueprint.get('a2a', {})
    tools = blueprint.get('tools', [])
    
    if len(tools) > 1 and not a2a.get('required_capabilities'):
        errors.append("Multi-tool agents should specify A2A required_capabilities")
    
    if errors:
        print(f"❌ {file_path}: Semantic validation failed")
        for error in errors:
            print(f"   - {error}")
        return False
    
    return True


def main():
    """Main validation function."""
    if len(sys.argv) < 2:
        print("Usage: validate_blueprints.py <blueprint_file> [blueprint_file...]")
        sys.exit(1)
    
    schema = load_schema()
    all_valid = True
    
    for file_arg in sys.argv[1:]:
        file_path = Path(file_arg)
        
        if not file_path.exists():
            print(f"❌ {file_path}: File not found")
            all_valid = False
            continue
        
        if not file_path.suffix.lower() in ['.yaml', '.yml']:
            print(f"⚠️  {file_path}: Skipping non-YAML file")
            continue
        
        blueprint = load_blueprint(file_path)
        if blueprint is None:
            all_valid = False
            continue
        
        # JSONSchema validation
        schema_valid = validate_blueprint(blueprint, schema, file_path)
        
        # Semantic validation
        semantic_valid = validate_semantic_rules(blueprint, file_path)
        
        if not (schema_valid and semantic_valid):
            all_valid = False
    
    if not all_valid:
        print("\n❌ Blueprint validation failed")
        sys.exit(1)
    else:
        print("\n✅ All blueprints are valid")


if __name__ == "__main__":
    main()
