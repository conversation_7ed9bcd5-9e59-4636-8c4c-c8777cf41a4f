-- Stored procedures for Metamorphic Foundry BigQuery analytics
-- Implements data processing and aggregation routines for observability

-- Procedure to calculate agent performance metrics
CREATE OR REPLACE PROCEDURE `{PROJECT_ID}.foundry_observability.calculate_agent_metrics`(
  start_date DATE,
  end_date DATE
)
BEGIN
  -- Calculate and store daily agent performance metrics
  CREATE OR REPLACE TABLE `{PROJECT_ID}.foundry_observability.agent_metrics_daily` AS
  SELECT
    agent_name,
    agent_version,
    model,
    environment,
    DATE(timestamp) as metric_date,
    
    -- Volume metrics
    COUNT(*) as total_turns,
    COUNT(DISTINCT agent_id) as unique_instances,
    
    -- Performance metrics
    AVG(processing_time_ms) as avg_processing_time_ms,
    PERCENTILE_CONT(processing_time_ms, 0.50) OVER (PARTITION BY agent_name, DATE(timestamp)) as p50_processing_time_ms,
    PERCENTILE_CONT(processing_time_ms, 0.95) OVER (PARTITION BY agent_name, DATE(timestamp)) as p95_processing_time_ms,
    PERCENTILE_CONT(processing_time_ms, 0.99) OVER (PARTITION BY agent_name, DATE(timestamp)) as p99_processing_time_ms,
    
    -- Quality metrics
    AVG(CASE WHEN hallucination_rate IS NOT NULL THEN hallucination_rate END) as avg_hallucination_rate,
    MAX(CASE WHEN hallucination_rate IS NOT NULL THEN hallucination_rate END) as max_hallucination_rate,
    AVG(CASE WHEN toxicity_score IS NOT NULL THEN toxicity_score END) as avg_toxicity_score,
    MAX(CASE WHEN toxicity_score IS NOT NULL THEN toxicity_score END) as max_toxicity_score,
    AVG(CASE WHEN user_satisfaction IS NOT NULL THEN user_satisfaction END) as avg_user_satisfaction,
    
    -- Cost metrics
    SUM(token_count) as total_tokens,
    AVG(token_count) as avg_tokens_per_turn,
    SUM(cost_usd) as total_cost_usd,
    AVG(cost_usd) as avg_cost_per_turn,
    
    -- Error metrics
    SUM(CASE WHEN error_message IS NOT NULL THEN 1 ELSE 0 END) as error_count,
    SUM(CASE WHEN error_message IS NULL THEN 1 ELSE 0 END) as success_count,
    SAFE_DIVIDE(
      SUM(CASE WHEN error_message IS NOT NULL THEN 1 ELSE 0 END),
      COUNT(*)
    ) * 100 as error_rate_percentage,
    
    -- Security metrics
    SUM(CASE WHEN model_armor_verdict = 'BLOCK' THEN 1 ELSE 0 END) as blocked_count,
    SUM(CASE WHEN model_armor_verdict = 'ALLOW' THEN 1 ELSE 0 END) as allowed_count,
    SAFE_DIVIDE(
      SUM(CASE WHEN model_armor_verdict = 'BLOCK' THEN 1 ELSE 0 END),
      COUNT(*)
    ) * 100 as block_rate_percentage,
    
    -- Tool usage
    ARRAY_AGG(DISTINCT tools_used IGNORE NULLS) as unique_tools_used,
    AVG(ARRAY_LENGTH(tools_used)) as avg_tools_per_turn,
    
    -- Compliance indicators
    CASE 
      WHEN AVG(CASE WHEN hallucination_rate IS NOT NULL THEN hallucination_rate END) <= 0.02 
        AND AVG(CASE WHEN toxicity_score IS NOT NULL THEN toxicity_score END) <= 0.05
      THEN 'COMPLIANT'
      ELSE 'NON_COMPLIANT'
    END as compliance_status,
    
    CURRENT_TIMESTAMP() as calculated_at
    
  FROM `{PROJECT_ID}.foundry_observability.agent_turns`
  WHERE DATE(timestamp) BETWEEN start_date AND end_date
  GROUP BY agent_name, agent_version, model, environment, DATE(timestamp);
  
  -- Log the calculation
  INSERT INTO `{PROJECT_ID}.foundry_observability.calculation_log` (
    procedure_name,
    start_date,
    end_date,
    rows_processed,
    calculated_at
  )
  SELECT
    'calculate_agent_metrics',
    start_date,
    end_date,
    COUNT(*),
    CURRENT_TIMESTAMP()
  FROM `{PROJECT_ID}.foundry_observability.agent_metrics_daily`
  WHERE metric_date BETWEEN start_date AND end_date;
  
END;

-- Procedure to detect anomalies in agent behavior
CREATE OR REPLACE PROCEDURE `{PROJECT_ID}.foundry_observability.detect_anomalies`(
  lookback_days INT64
)
BEGIN
  DECLARE anomaly_threshold_multiplier FLOAT64 DEFAULT 3.0;
  
  -- Calculate baseline metrics for anomaly detection
  CREATE OR REPLACE TEMP TABLE baseline_metrics AS
  SELECT
    agent_name,
    model,
    environment,
    AVG(processing_time_ms) as baseline_avg_processing_time,
    STDDEV(processing_time_ms) as baseline_stddev_processing_time,
    AVG(CASE WHEN hallucination_rate IS NOT NULL THEN hallucination_rate END) as baseline_avg_hallucination,
    STDDEV(CASE WHEN hallucination_rate IS NOT NULL THEN hallucination_rate END) as baseline_stddev_hallucination,
    AVG(CASE WHEN toxicity_score IS NOT NULL THEN toxicity_score END) as baseline_avg_toxicity,
    STDDEV(CASE WHEN toxicity_score IS NOT NULL THEN toxicity_score END) as baseline_stddev_toxicity,
    COUNT(*) as baseline_sample_size
  FROM `{PROJECT_ID}.foundry_observability.agent_turns`
  WHERE timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL lookback_days DAY)
    AND timestamp < TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 DAY)  -- Exclude today
  GROUP BY agent_name, model, environment
  HAVING COUNT(*) >= 100;  -- Minimum sample size for reliable statistics
  
  -- Detect current anomalies
  CREATE OR REPLACE TABLE `{PROJECT_ID}.foundry_observability.anomaly_alerts` AS
  WITH current_metrics AS (
    SELECT
      agent_name,
      model,
      environment,
      agent_id,
      timestamp,
      processing_time_ms,
      hallucination_rate,
      toxicity_score,
      error_message
    FROM `{PROJECT_ID}.foundry_observability.agent_turns`
    WHERE timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 HOUR)
  )
  
  SELECT
    c.agent_name,
    c.model,
    c.environment,
    c.agent_id,
    c.timestamp,
    
    -- Anomaly flags
    CASE 
      WHEN c.processing_time_ms > (b.baseline_avg_processing_time + anomaly_threshold_multiplier * b.baseline_stddev_processing_time)
      THEN TRUE ELSE FALSE 
    END as processing_time_anomaly,
    
    CASE 
      WHEN c.hallucination_rate > (b.baseline_avg_hallucination + anomaly_threshold_multiplier * b.baseline_stddev_hallucination)
      THEN TRUE ELSE FALSE 
    END as hallucination_anomaly,
    
    CASE 
      WHEN c.toxicity_score > (b.baseline_avg_toxicity + anomaly_threshold_multiplier * b.baseline_stddev_toxicity)
      THEN TRUE ELSE FALSE 
    END as toxicity_anomaly,
    
    CASE 
      WHEN c.error_message IS NOT NULL THEN TRUE ELSE FALSE 
    END as error_anomaly,
    
    -- Actual vs baseline values
    c.processing_time_ms,
    b.baseline_avg_processing_time,
    c.hallucination_rate,
    b.baseline_avg_hallucination,
    c.toxicity_score,
    b.baseline_avg_toxicity,
    
    -- Severity scoring
    CASE 
      WHEN (c.processing_time_ms > (b.baseline_avg_processing_time + anomaly_threshold_multiplier * b.baseline_stddev_processing_time))
        OR (c.hallucination_rate > (b.baseline_avg_hallucination + anomaly_threshold_multiplier * b.baseline_stddev_hallucination))
        OR (c.toxicity_score > (b.baseline_avg_toxicity + anomaly_threshold_multiplier * b.baseline_stddev_toxicity))
        OR (c.error_message IS NOT NULL)
      THEN 'HIGH'
      WHEN (c.processing_time_ms > (b.baseline_avg_processing_time + 2.0 * b.baseline_stddev_processing_time))
        OR (c.hallucination_rate > (b.baseline_avg_hallucination + 2.0 * b.baseline_stddev_hallucination))
        OR (c.toxicity_score > (b.baseline_avg_toxicity + 2.0 * b.baseline_stddev_toxicity))
      THEN 'MEDIUM'
      ELSE 'LOW'
    END as anomaly_severity,
    
    CURRENT_TIMESTAMP() as detected_at
    
  FROM current_metrics c
  INNER JOIN baseline_metrics b 
    ON c.agent_name = b.agent_name 
    AND c.model = b.model 
    AND c.environment = b.environment
  WHERE 
    -- Only flag actual anomalies
    (c.processing_time_ms > (b.baseline_avg_processing_time + anomaly_threshold_multiplier * b.baseline_stddev_processing_time))
    OR (c.hallucination_rate > (b.baseline_avg_hallucination + anomaly_threshold_multiplier * b.baseline_stddev_hallucination))
    OR (c.toxicity_score > (b.baseline_avg_toxicity + anomaly_threshold_multiplier * b.baseline_stddev_toxicity))
    OR (c.error_message IS NOT NULL);
    
END;

-- Procedure to generate compliance reports
CREATE OR REPLACE PROCEDURE `{PROJECT_ID}.foundry_observability.generate_compliance_report`(
  report_date DATE
)
BEGIN
  -- Generate daily compliance report
  CREATE OR REPLACE TABLE `{PROJECT_ID}.foundry_observability.compliance_report_daily` AS
  WITH agent_compliance AS (
    SELECT
      agent_name,
      agent_version,
      environment,
      COUNT(*) as total_turns,
      
      -- Hallucination compliance
      AVG(CASE WHEN hallucination_rate IS NOT NULL THEN hallucination_rate END) as avg_hallucination_rate,
      SUM(CASE WHEN hallucination_rate <= 0.02 THEN 1 ELSE 0 END) as hallucination_compliant_turns,
      
      -- Toxicity compliance  
      AVG(CASE WHEN toxicity_score IS NOT NULL THEN toxicity_score END) as avg_toxicity_score,
      SUM(CASE WHEN toxicity_score <= 0.05 THEN 1 ELSE 0 END) as toxicity_compliant_turns,
      
      -- Latency compliance (model-specific)
      CASE 
        WHEN model = 'gemini-2.5-flash' THEN
          SUM(CASE WHEN processing_time_ms <= 1000 THEN 1 ELSE 0 END)
        WHEN model = 'gemini-2.5-pro' THEN
          SUM(CASE WHEN processing_time_ms <= 8000 THEN 1 ELSE 0 END)
        ELSE
          SUM(CASE WHEN processing_time_ms <= 2000 THEN 1 ELSE 0 END)
      END as latency_compliant_turns,
      
      -- Security compliance
      SUM(CASE WHEN model_armor_verdict != 'BLOCK' THEN 1 ELSE 0 END) as security_compliant_turns,
      
      model
      
    FROM `{PROJECT_ID}.foundry_observability.agent_turns`
    WHERE DATE(timestamp) = report_date
    GROUP BY agent_name, agent_version, environment, model
  ),
  
  violation_summary AS (
    SELECT
      COUNT(*) as total_violations,
      SUM(CASE WHEN severity = 'HIGH' THEN 1 ELSE 0 END) as high_severity_violations,
      SUM(CASE WHEN severity = 'MEDIUM' THEN 1 ELSE 0 END) as medium_severity_violations,
      SUM(CASE WHEN severity = 'LOW' THEN 1 ELSE 0 END) as low_severity_violations
    FROM `{PROJECT_ID}.foundry_observability.policy_violations`
    WHERE DATE(timestamp) = report_date
  )
  
  SELECT
    report_date,
    a.agent_name,
    a.agent_version,
    a.environment,
    a.model,
    a.total_turns,
    
    -- Compliance percentages
    SAFE_DIVIDE(a.hallucination_compliant_turns, a.total_turns) * 100 as hallucination_compliance_pct,
    SAFE_DIVIDE(a.toxicity_compliant_turns, a.total_turns) * 100 as toxicity_compliance_pct,
    SAFE_DIVIDE(a.latency_compliant_turns, a.total_turns) * 100 as latency_compliance_pct,
    SAFE_DIVIDE(a.security_compliant_turns, a.total_turns) * 100 as security_compliance_pct,
    
    -- Overall compliance score
    (
      SAFE_DIVIDE(a.hallucination_compliant_turns, a.total_turns) * 25 +
      SAFE_DIVIDE(a.toxicity_compliant_turns, a.total_turns) * 25 +
      SAFE_DIVIDE(a.latency_compliant_turns, a.total_turns) * 25 +
      SAFE_DIVIDE(a.security_compliant_turns, a.total_turns) * 25
    ) as overall_compliance_score,
    
    -- Compliance status
    CASE 
      WHEN (
        SAFE_DIVIDE(a.hallucination_compliant_turns, a.total_turns) >= 0.98 AND
        SAFE_DIVIDE(a.toxicity_compliant_turns, a.total_turns) >= 0.95 AND
        SAFE_DIVIDE(a.latency_compliant_turns, a.total_turns) >= 0.95 AND
        SAFE_DIVIDE(a.security_compliant_turns, a.total_turns) >= 0.99
      ) THEN 'FULLY_COMPLIANT'
      WHEN (
        SAFE_DIVIDE(a.hallucination_compliant_turns, a.total_turns) >= 0.95 AND
        SAFE_DIVIDE(a.toxicity_compliant_turns, a.total_turns) >= 0.90 AND
        SAFE_DIVIDE(a.latency_compliant_turns, a.total_turns) >= 0.90 AND
        SAFE_DIVIDE(a.security_compliant_turns, a.total_turns) >= 0.95
      ) THEN 'MOSTLY_COMPLIANT'
      ELSE 'NON_COMPLIANT'
    END as compliance_status,
    
    -- Violation context
    v.total_violations,
    v.high_severity_violations,
    v.medium_severity_violations,
    v.low_severity_violations,
    
    CURRENT_TIMESTAMP() as generated_at
    
  FROM agent_compliance a
  CROSS JOIN violation_summary v
  ORDER BY overall_compliance_score DESC;
  
END;

-- Procedure to cleanup old data based on retention policies
CREATE OR REPLACE PROCEDURE `{PROJECT_ID}.foundry_observability.cleanup_old_data`()
BEGIN
  DECLARE retention_days INT64 DEFAULT 400;
  DECLARE cleanup_date DATE;
  
  SET cleanup_date = DATE_SUB(CURRENT_DATE(), INTERVAL retention_days DAY);
  
  -- Delete old agent turns
  DELETE FROM `{PROJECT_ID}.foundry_observability.agent_turns`
  WHERE DATE(timestamp) < cleanup_date;
  
  -- Delete old deployments
  DELETE FROM `{PROJECT_ID}.foundry_observability.agent_deployments`
  WHERE DATE(timestamp) < cleanup_date;
  
  -- Delete old policy violations
  DELETE FROM `{PROJECT_ID}.foundry_observability.policy_violations`
  WHERE DATE(timestamp) < cleanup_date;
  
  -- Delete old calculated metrics
  DELETE FROM `{PROJECT_ID}.foundry_observability.agent_metrics_daily`
  WHERE metric_date < cleanup_date;
  
  -- Delete old compliance reports
  DELETE FROM `{PROJECT_ID}.foundry_observability.compliance_report_daily`
  WHERE report_date < cleanup_date;
  
  -- Delete old anomaly alerts (shorter retention)
  DELETE FROM `{PROJECT_ID}.foundry_observability.anomaly_alerts`
  WHERE DATE(detected_at) < DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY);
  
  -- Log cleanup operation
  INSERT INTO `{PROJECT_ID}.foundry_observability.calculation_log` (
    procedure_name,
    start_date,
    end_date,
    rows_processed,
    calculated_at
  )
  VALUES (
    'cleanup_old_data',
    cleanup_date,
    CURRENT_DATE(),
    0,  -- Would need to capture actual deleted row counts
    CURRENT_TIMESTAMP()
  );
  
END;
