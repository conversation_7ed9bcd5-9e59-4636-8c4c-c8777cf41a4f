# BigQuery resources for Metamorphic Foundry observability
# Implements Evaluation Playbook §4: BigQuery agent_turn table with 400-day retention

# BigQuery dataset for agent observability
resource "google_bigquery_dataset" "foundry_observability" {
  dataset_id                 = "foundry_observability"
  friendly_name              = "Metamorphic Foundry Observability"
  description                = "Agent turn logs, evaluation metrics, and audit trails"
  location                   = "US"
  delete_contents_on_destroy = false
  
  labels = local.labels
  
  # 400-day retention as per Evaluation Playbook
  default_table_expiration_ms = ***********  # 400 days in milliseconds
  
  access {
    role          = "OWNER"
    user_by_email = data.google_client_config.default.access_token
  }
  
  access {
    role   = "READER"
    domain = var.organization_domain
  }
  
  # Vertex AI service account access
  access {
    role   = "WRITER"
    user_by_email = "service-${data.google_project.project.number}@gcp-sa-aiplatform.iam.gserviceaccount.com"
  }
  
  depends_on = [google_project_service.apis]
}

# Agent turn logs table
resource "google_bigquery_table" "agent_turns" {
  dataset_id          = google_bigquery_dataset.foundry_observability.dataset_id
  table_id            = "agent_turns"
  deletion_protection = false
  
  description = "Individual agent conversation turns with evaluation metrics"
  
  labels = local.labels
  
  # Partition by date for performance
  time_partitioning {
    type  = "DAY"
    field = "timestamp"
  }
  
  # Cluster by agent for query optimization
  clustering = ["agent_name", "model", "environment"]
  
  schema = jsonencode([
    {
      name = "agent_id"
      type = "STRING"
      mode = "REQUIRED"
      description = "Unique identifier for the agent instance"
    },
    {
      name = "request_id"
      type = "STRING"
      mode = "REQUIRED"
      description = "Unique identifier for the request"
    },
    {
      name = "agent_name"
      type = "STRING"
      mode = "REQUIRED"
      description = "Name of the agent from blueprint"
    },
    {
      name = "agent_version"
      type = "STRING"
      mode = "REQUIRED"
      description = "Version of the agent blueprint"
    },
    {
      name = "model"
      type = "STRING"
      mode = "REQUIRED"
      description = "Model used (gemini-2.5-pro, gemini-2.5-flash, etc.)"
    },
    {
      name = "environment"
      type = "STRING"
      mode = "REQUIRED"
      description = "Deployment environment (staging, prod)"
    },
    {
      name = "timestamp"
      type = "TIMESTAMP"
      mode = "REQUIRED"
      description = "When the turn occurred"
    },
    {
      name = "user_message_hash"
      type = "STRING"
      mode = "NULLABLE"
      description = "Hash of user input (for privacy)"
    },
    {
      name = "response_hash"
      type = "STRING"
      mode = "NULLABLE"
      description = "Hash of agent response (for privacy)"
    },
    {
      name = "processing_time_ms"
      type = "FLOAT"
      mode = "REQUIRED"
      description = "Processing time in milliseconds"
    },
    {
      name = "token_count"
      type = "INTEGER"
      mode = "NULLABLE"
      description = "Total tokens used in the turn"
    },
    {
      name = "cost_usd"
      type = "FLOAT"
      mode = "NULLABLE"
      description = "Cost of the turn in USD"
    },
    {
      name = "hallucination_rate"
      type = "FLOAT"
      mode = "NULLABLE"
      description = "Hallucination rate metric (0.0-1.0)"
    },
    {
      name = "toxicity_score"
      type = "FLOAT"
      mode = "NULLABLE"
      description = "Toxicity score metric (0.0-1.0)"
    },
    {
      name = "user_satisfaction"
      type = "FLOAT"
      mode = "NULLABLE"
      description = "User satisfaction rating (1.0-5.0)"
    },
    {
      name = "model_armor_verdict"
      type = "STRING"
      mode = "NULLABLE"
      description = "Model Armor policy verdict (ALLOW, BLOCK, etc.)"
    },
    {
      name = "tools_used"
      type = "STRING"
      mode = "REPEATED"
      description = "List of tools invoked during the turn"
    },
    {
      name = "error_message"
      type = "STRING"
      mode = "NULLABLE"
      description = "Error message if turn failed"
    },
    {
      name = "metadata"
      type = "JSON"
      mode = "NULLABLE"
      description = "Additional metadata as JSON"
    }
  ])
}

# Agent deployment events table
resource "google_bigquery_table" "agent_deployments" {
  dataset_id          = google_bigquery_dataset.foundry_observability.dataset_id
  table_id            = "agent_deployments"
  deletion_protection = false
  
  description = "Agent deployment events and A2A manifest logs"
  
  labels = local.labels
  
  time_partitioning {
    type  = "DAY"
    field = "timestamp"
  }
  
  schema = jsonencode([
    {
      name = "deployment_id"
      type = "STRING"
      mode = "REQUIRED"
      description = "Unique deployment identifier"
    },
    {
      name = "agent_name"
      type = "STRING"
      mode = "REQUIRED"
      description = "Name of the deployed agent"
    },
    {
      name = "agent_version"
      type = "STRING"
      mode = "REQUIRED"
      description = "Version of the deployed agent"
    },
    {
      name = "environment"
      type = "STRING"
      mode = "REQUIRED"
      description = "Target environment"
    },
    {
      name = "timestamp"
      type = "TIMESTAMP"
      mode = "REQUIRED"
      description = "Deployment timestamp"
    },
    {
      name = "git_commit"
      type = "STRING"
      mode = "NULLABLE"
      description = "Git commit SHA"
    },
    {
      name = "git_branch"
      type = "STRING"
      mode = "NULLABLE"
      description = "Git branch name"
    },
    {
      name = "deployment_status"
      type = "STRING"
      mode = "REQUIRED"
      description = "Deployment status (success, failed, rollback)"
    },
    {
      name = "a2a_manifest"
      type = "JSON"
      mode = "NULLABLE"
      description = "Signed A2A manifest for traceability"
    },
    {
      name = "canary_pass_rate"
      type = "FLOAT"
      mode = "NULLABLE"
      description = "Canary test pass rate percentage"
    },
    {
      name = "deployed_by"
      type = "STRING"
      mode = "NULLABLE"
      description = "User or service account that triggered deployment"
    }
  ])
}

# Policy violations table
resource "google_bigquery_table" "policy_violations" {
  dataset_id          = google_bigquery_dataset.foundry_observability.dataset_id
  table_id            = "policy_violations"
  deletion_protection = false
  
  description = "Model Armor and OPA policy violations"
  
  labels = local.labels
  
  time_partitioning {
    type  = "DAY"
    field = "timestamp"
  }
  
  schema = jsonencode([
    {
      name = "violation_id"
      type = "STRING"
      mode = "REQUIRED"
      description = "Unique violation identifier"
    },
    {
      name = "agent_id"
      type = "STRING"
      mode = "REQUIRED"
      description = "Agent that triggered the violation"
    },
    {
      name = "violation_type"
      type = "STRING"
      mode = "REQUIRED"
      description = "Type of violation (model_armor, opa_policy, etc.)"
    },
    {
      name = "severity"
      type = "STRING"
      mode = "REQUIRED"
      description = "Violation severity (LOW, MEDIUM, HIGH, CRITICAL)"
    },
    {
      name = "timestamp"
      type = "TIMESTAMP"
      mode = "REQUIRED"
      description = "When the violation occurred"
    },
    {
      name = "policy_name"
      type = "STRING"
      mode = "NULLABLE"
      description = "Name of the violated policy"
    },
    {
      name = "violation_details"
      type = "JSON"
      mode = "NULLABLE"
      description = "Detailed violation information"
    },
    {
      name = "action_taken"
      type = "STRING"
      mode = "NULLABLE"
      description = "Action taken (block, allow, log)"
    }
  ])
}
