# BigQuery views and stored procedures for Metamorphic Foundry analytics
# Implements pre-built analytics queries for Looker dashboards

# Agent performance summary view
resource "google_bigquery_table" "agent_performance_summary" {
  dataset_id = google_bigquery_dataset.foundry_observability.dataset_id
  table_id   = "agent_performance_summary"
  
  description = "Aggregated agent performance metrics for dashboard consumption"
  
  labels = local.labels
  
  view {
    query = <<-SQL
      SELECT
        agent_name,
        agent_version,
        model,
        environment,
        DATE(timestamp) as date,
        COUNT(*) as total_turns,
        AVG(processing_time_ms) as avg_processing_time_ms,
        PERCENTILE_CONT(processing_time_ms, 0.95) OVER (PARTITION BY agent_name, DATE(timestamp)) as p95_processing_time_ms,
        AVG(CASE WHEN hallucination_rate IS NOT NULL THEN hallucination_rate END) as avg_hallucination_rate,
        AVG(CASE WHEN toxicity_score IS NOT NULL THEN toxicity_score END) as avg_toxicity_score,
        AVG(CASE WHEN user_satisfaction IS NOT NULL THEN user_satisfaction END) as avg_user_satisfaction,
        SUM(CASE WHEN error_message IS NOT NULL THEN 1 ELSE 0 END) as error_count,
        SUM(CASE WHEN error_message IS NULL THEN 1 ELSE 0 END) as success_count,
        SUM(CASE WHEN model_armor_verdict = 'BLOCK' THEN 1 ELSE 0 END) as blocked_count,
        SUM(token_count) as total_tokens,
        SUM(cost_usd) as total_cost_usd,
        ARRAY_AGG(DISTINCT tools_used IGNORE NULLS) as tools_used_list
      FROM `${var.project_id}.${google_bigquery_dataset.foundry_observability.dataset_id}.${google_bigquery_table.agent_turns.table_id}`
      WHERE timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY)
      GROUP BY agent_name, agent_version, model, environment, DATE(timestamp)
      ORDER BY date DESC, total_turns DESC
    SQL
    
    use_legacy_sql = false
  }
  
  depends_on = [google_bigquery_table.agent_turns]
}

# Model efficiency view
resource "google_bigquery_table" "model_efficiency_view" {
  dataset_id = google_bigquery_dataset.foundry_observability.dataset_id
  table_id   = "model_efficiency_view"
  
  description = "Model usage efficiency and routing analytics"
  
  labels = local.labels
  
  view {
    query = <<-SQL
      SELECT
        model,
        DATE(timestamp) as date,
        COUNT(*) as total_requests,
        AVG(processing_time_ms) as avg_latency_ms,
        PERCENTILE_CONT(processing_time_ms, 0.95) OVER (PARTITION BY model, DATE(timestamp)) as p95_latency_ms,
        SUM(token_count) as total_tokens,
        SUM(cost_usd) as total_cost_usd,
        AVG(cost_usd) as avg_cost_per_request,
        SUM(token_count) / COUNT(*) as avg_tokens_per_request,
        
        -- Efficiency metrics
        CASE 
          WHEN model = 'gemini-2.5-flash' THEN 
            COUNT(*) / NULLIF(SUM(COUNT(*)) OVER (PARTITION BY DATE(timestamp)), 0) * 100
          ELSE 0 
        END as flash_usage_percentage,
        
        CASE 
          WHEN model = 'gemma-3n' THEN 
            COUNT(*) / NULLIF(SUM(COUNT(*)) OVER (PARTITION BY DATE(timestamp)), 0) * 100
          ELSE 0 
        END as edge_usage_percentage,
        
        -- Quality metrics
        AVG(CASE WHEN hallucination_rate IS NOT NULL THEN hallucination_rate END) as avg_hallucination_rate,
        AVG(CASE WHEN toxicity_score IS NOT NULL THEN toxicity_score END) as avg_toxicity_score,
        
        -- Error rates
        SUM(CASE WHEN error_message IS NOT NULL THEN 1 ELSE 0 END) / COUNT(*) * 100 as error_rate_percentage
        
      FROM `${var.project_id}.${google_bigquery_dataset.foundry_observability.dataset_id}.${google_bigquery_table.agent_turns.table_id}`
      WHERE timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY)
      GROUP BY model, DATE(timestamp)
      ORDER BY date DESC, total_requests DESC
    SQL
    
    use_legacy_sql = false
  }
  
  depends_on = [google_bigquery_table.agent_turns]
}

# Policy compliance view
resource "google_bigquery_table" "policy_compliance_view" {
  dataset_id = google_bigquery_dataset.foundry_observability.dataset_id
  table_id   = "policy_compliance_view"
  
  description = "Policy violations and compliance metrics"
  
  labels = local.labels
  
  view {
    query = <<-SQL
      WITH violation_summary AS (
        SELECT
          DATE(timestamp) as date,
          violation_type,
          severity,
          COUNT(*) as violation_count,
          COUNT(DISTINCT agent_id) as affected_agents
        FROM `${var.project_id}.${google_bigquery_dataset.foundry_observability.dataset_id}.${google_bigquery_table.policy_violations.table_id}`
        WHERE timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY)
        GROUP BY DATE(timestamp), violation_type, severity
      ),
      
      turn_summary AS (
        SELECT
          DATE(timestamp) as date,
          COUNT(*) as total_turns,
          SUM(CASE WHEN model_armor_verdict = 'BLOCK' THEN 1 ELSE 0 END) as blocked_turns,
          SUM(CASE WHEN model_armor_verdict = 'ALLOW' THEN 1 ELSE 0 END) as allowed_turns,
          AVG(CASE WHEN hallucination_rate IS NOT NULL THEN hallucination_rate END) as avg_hallucination_rate,
          AVG(CASE WHEN toxicity_score IS NOT NULL THEN toxicity_score END) as avg_toxicity_score
        FROM `${var.project_id}.${google_bigquery_dataset.foundry_observability.dataset_id}.${google_bigquery_table.agent_turns.table_id}`
        WHERE timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY)
        GROUP BY DATE(timestamp)
      )
      
      SELECT
        COALESCE(v.date, t.date) as date,
        COALESCE(v.violation_count, 0) as total_violations,
        COALESCE(v.affected_agents, 0) as affected_agents,
        t.total_turns,
        t.blocked_turns,
        t.allowed_turns,
        SAFE_DIVIDE(t.blocked_turns, t.total_turns) * 100 as block_rate_percentage,
        t.avg_hallucination_rate,
        t.avg_toxicity_score,
        
        -- Compliance score (higher is better)
        CASE 
          WHEN t.avg_hallucination_rate <= 0.02 AND t.avg_toxicity_score <= 0.05 
            AND SAFE_DIVIDE(t.blocked_turns, t.total_turns) <= 0.01 
          THEN 100
          WHEN t.avg_hallucination_rate <= 0.05 AND t.avg_toxicity_score <= 0.1 
            AND SAFE_DIVIDE(t.blocked_turns, t.total_turns) <= 0.05 
          THEN 75
          WHEN t.avg_hallucination_rate <= 0.1 AND t.avg_toxicity_score <= 0.2 
            AND SAFE_DIVIDE(t.blocked_turns, t.total_turns) <= 0.1 
          THEN 50
          ELSE 25
        END as compliance_score,
        
        v.violation_type,
        v.severity
        
      FROM violation_summary v
      FULL OUTER JOIN turn_summary t ON v.date = t.date
      ORDER BY date DESC
    SQL
    
    use_legacy_sql = false
  }
  
  depends_on = [
    google_bigquery_table.agent_turns,
    google_bigquery_table.policy_violations
  ]
}

# Deployment analytics view
resource "google_bigquery_table" "deployment_analytics_view" {
  dataset_id = google_bigquery_dataset.foundry_observability.dataset_id
  table_id   = "deployment_analytics_view"
  
  description = "Agent deployment success rates and A2A manifest tracking"
  
  labels = local.labels
  
  view {
    query = <<-SQL
      SELECT
        agent_name,
        agent_version,
        environment,
        DATE(timestamp) as deployment_date,
        COUNT(*) as total_deployments,
        SUM(CASE WHEN deployment_status = 'success' THEN 1 ELSE 0 END) as successful_deployments,
        SUM(CASE WHEN deployment_status = 'failed' THEN 1 ELSE 0 END) as failed_deployments,
        SUM(CASE WHEN deployment_status = 'rollback' THEN 1 ELSE 0 END) as rollback_deployments,
        
        -- Success rate
        SAFE_DIVIDE(
          SUM(CASE WHEN deployment_status = 'success' THEN 1 ELSE 0 END),
          COUNT(*)
        ) * 100 as success_rate_percentage,
        
        -- Average canary pass rate
        AVG(CASE WHEN canary_pass_rate IS NOT NULL THEN canary_pass_rate END) as avg_canary_pass_rate,
        
        -- Time to deployment (simplified - would need more complex logic in practice)
        COUNT(DISTINCT git_commit) as unique_commits,
        COUNT(DISTINCT deployed_by) as unique_deployers,
        
        -- A2A manifest tracking
        COUNT(CASE WHEN a2a_manifest IS NOT NULL THEN 1 END) as deployments_with_a2a,
        
        -- Latest deployment info
        ARRAY_AGG(
          STRUCT(
            deployment_id,
            git_commit,
            git_branch,
            deployment_status,
            canary_pass_rate,
            deployed_by
          ) 
          ORDER BY timestamp DESC 
          LIMIT 1
        )[OFFSET(0)] as latest_deployment
        
      FROM `${var.project_id}.${google_bigquery_dataset.foundry_observability.dataset_id}.${google_bigquery_table.agent_deployments.table_id}`
      WHERE timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY)
      GROUP BY agent_name, agent_version, environment, DATE(timestamp)
      ORDER BY deployment_date DESC, total_deployments DESC
    SQL
    
    use_legacy_sql = false
  }
  
  depends_on = [google_bigquery_table.agent_deployments]
}

# Executive summary view
resource "google_bigquery_table" "executive_summary_view" {
  dataset_id = google_bigquery_dataset.foundry_observability.dataset_id
  table_id   = "executive_summary_view"
  
  description = "High-level KPIs and metrics for executive dashboards"
  
  labels = local.labels
  
  view {
    query = <<-SQL
      WITH daily_metrics AS (
        SELECT
          DATE(timestamp) as date,
          COUNT(*) as total_turns,
          COUNT(DISTINCT agent_name) as active_agents,
          AVG(processing_time_ms) as avg_processing_time_ms,
          PERCENTILE_CONT(processing_time_ms, 0.95) OVER (PARTITION BY DATE(timestamp)) as p95_processing_time_ms,
          SUM(token_count) as total_tokens,
          SUM(cost_usd) as total_cost_usd,
          
          -- Quality metrics
          AVG(CASE WHEN hallucination_rate IS NOT NULL THEN hallucination_rate END) as avg_hallucination_rate,
          AVG(CASE WHEN toxicity_score IS NOT NULL THEN toxicity_score END) as avg_toxicity_score,
          AVG(CASE WHEN user_satisfaction IS NOT NULL THEN user_satisfaction END) as avg_user_satisfaction,
          
          -- Model distribution
          SUM(CASE WHEN model = 'gemini-2.5-flash' THEN 1 ELSE 0 END) / COUNT(*) * 100 as flash_usage_pct,
          SUM(CASE WHEN model = 'gemma-3n' THEN 1 ELSE 0 END) / COUNT(*) * 100 as edge_usage_pct,
          
          -- Error rates
          SUM(CASE WHEN error_message IS NOT NULL THEN 1 ELSE 0 END) / COUNT(*) * 100 as error_rate_pct
          
        FROM `${var.project_id}.${google_bigquery_dataset.foundry_observability.dataset_id}.${google_bigquery_table.agent_turns.table_id}`
        WHERE timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY)
        GROUP BY DATE(timestamp)
      ),
      
      deployment_metrics AS (
        SELECT
          DATE(timestamp) as date,
          COUNT(*) as total_deployments,
          SUM(CASE WHEN deployment_status = 'success' THEN 1 ELSE 0 END) / COUNT(*) * 100 as deployment_success_rate
        FROM `${var.project_id}.${google_bigquery_dataset.foundry_observability.dataset_id}.${google_bigquery_table.agent_deployments.table_id}`
        WHERE timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY)
        GROUP BY DATE(timestamp)
      )
      
      SELECT
        m.date,
        m.total_turns,
        m.active_agents,
        m.avg_processing_time_ms,
        m.p95_processing_time_ms,
        m.total_tokens,
        m.total_cost_usd,
        m.avg_hallucination_rate,
        m.avg_toxicity_score,
        m.avg_user_satisfaction,
        m.flash_usage_pct,
        m.edge_usage_pct,
        m.error_rate_pct,
        COALESCE(d.total_deployments, 0) as total_deployments,
        COALESCE(d.deployment_success_rate, 100) as deployment_success_rate,
        
        -- KPI status indicators
        CASE 
          WHEN m.avg_hallucination_rate <= 0.02 THEN 'GREEN'
          WHEN m.avg_hallucination_rate <= 0.05 THEN 'YELLOW'
          ELSE 'RED'
        END as hallucination_status,
        
        CASE 
          WHEN m.avg_toxicity_score <= 0.05 THEN 'GREEN'
          WHEN m.avg_toxicity_score <= 0.1 THEN 'YELLOW'
          ELSE 'RED'
        END as toxicity_status,
        
        CASE 
          WHEN m.p95_processing_time_ms <= 1000 AND model = 'gemini-2.5-flash' THEN 'GREEN'
          WHEN m.p95_processing_time_ms <= 8000 AND model = 'gemini-2.5-pro' THEN 'GREEN'
          WHEN m.p95_processing_time_ms <= 2000 THEN 'YELLOW'
          ELSE 'RED'
        END as latency_status,
        
        CASE 
          WHEN (m.flash_usage_pct + m.edge_usage_pct) >= 40 THEN 'GREEN'
          WHEN (m.flash_usage_pct + m.edge_usage_pct) >= 20 THEN 'YELLOW'
          ELSE 'RED'
        END as efficiency_status
        
      FROM daily_metrics m
      LEFT JOIN deployment_metrics d ON m.date = d.date
      ORDER BY m.date DESC
    SQL
    
    use_legacy_sql = false
  }
  
  depends_on = [
    google_bigquery_table.agent_turns,
    google_bigquery_table.agent_deployments
  ]
}
