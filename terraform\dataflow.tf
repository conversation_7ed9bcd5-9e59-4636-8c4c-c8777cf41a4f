# Dataflow pipeline for streaming agent data to BigQuery
# Implements real-time observability data ingestion from Pub/Sub to BigQuery

# Pub/Sub topics for agent events
resource "google_pubsub_topic" "agent_turns" {
  name = "agent-turns"
  
  labels = local.labels
  
  message_retention_duration = "604800s"  # 7 days
  
  schema_settings {
    schema   = google_pubsub_schema.agent_turn_schema.id
    encoding = "JSON"
  }
  
  depends_on = [google_project_service.apis]
}

resource "google_pubsub_topic" "agent_deployments" {
  name = "agent-deployments"
  
  labels = local.labels
  
  message_retention_duration = "604800s"
  
  schema_settings {
    schema   = google_pubsub_schema.agent_deployment_schema.id
    encoding = "JSON"
  }
  
  depends_on = [google_project_service.apis]
}

resource "google_pubsub_topic" "policy_violations" {
  name = "policy-violations"
  
  labels = local.labels
  
  message_retention_duration = "2592000s"  # 30 days for compliance
  
  schema_settings {
    schema   = google_pubsub_schema.policy_violation_schema.id
    encoding = "JSON"
  }
  
  depends_on = [google_project_service.apis]
}

# Pub/Sub schemas for data validation
resource "google_pubsub_schema" "agent_turn_schema" {
  name       = "agent-turn-schema"
  type       = "AVRO"
  definition = jsonencode({
    type = "record"
    name = "AgentTurn"
    fields = [
      {
        name = "agent_id"
        type = "string"
      },
      {
        name = "request_id"
        type = "string"
      },
      {
        name = "agent_name"
        type = "string"
      },
      {
        name = "agent_version"
        type = "string"
      },
      {
        name = "model"
        type = "string"
      },
      {
        name = "environment"
        type = "string"
      },
      {
        name = "timestamp"
        type = "string"
      },
      {
        name = "user_message_hash"
        type = ["null", "string"]
        default = null
      },
      {
        name = "response_hash"
        type = ["null", "string"]
        default = null
      },
      {
        name = "processing_time_ms"
        type = "double"
      },
      {
        name = "token_count"
        type = ["null", "int"]
        default = null
      },
      {
        name = "cost_usd"
        type = ["null", "double"]
        default = null
      },
      {
        name = "hallucination_rate"
        type = ["null", "double"]
        default = null
      },
      {
        name = "toxicity_score"
        type = ["null", "double"]
        default = null
      },
      {
        name = "user_satisfaction"
        type = ["null", "double"]
        default = null
      },
      {
        name = "model_armor_verdict"
        type = ["null", "string"]
        default = null
      },
      {
        name = "tools_used"
        type = {
          type = "array"
          items = "string"
        }
        default = []
      },
      {
        name = "error_message"
        type = ["null", "string"]
        default = null
      },
      {
        name = "metadata"
        type = ["null", "string"]
        default = null
      }
    ]
  })
  
  depends_on = [google_project_service.apis]
}

resource "google_pubsub_schema" "agent_deployment_schema" {
  name       = "agent-deployment-schema"
  type       = "AVRO"
  definition = jsonencode({
    type = "record"
    name = "AgentDeployment"
    fields = [
      {
        name = "deployment_id"
        type = "string"
      },
      {
        name = "agent_name"
        type = "string"
      },
      {
        name = "agent_version"
        type = "string"
      },
      {
        name = "environment"
        type = "string"
      },
      {
        name = "timestamp"
        type = "string"
      },
      {
        name = "git_commit"
        type = ["null", "string"]
        default = null
      },
      {
        name = "git_branch"
        type = ["null", "string"]
        default = null
      },
      {
        name = "deployment_status"
        type = "string"
      },
      {
        name = "a2a_manifest"
        type = ["null", "string"]
        default = null
      },
      {
        name = "canary_pass_rate"
        type = ["null", "double"]
        default = null
      },
      {
        name = "deployed_by"
        type = ["null", "string"]
        default = null
      }
    ]
  })
  
  depends_on = [google_project_service.apis]
}

resource "google_pubsub_schema" "policy_violation_schema" {
  name       = "policy-violation-schema"
  type       = "AVRO"
  definition = jsonencode({
    type = "record"
    name = "PolicyViolation"
    fields = [
      {
        name = "violation_id"
        type = "string"
      },
      {
        name = "agent_id"
        type = "string"
      },
      {
        name = "violation_type"
        type = "string"
      },
      {
        name = "severity"
        type = "string"
      },
      {
        name = "timestamp"
        type = "string"
      },
      {
        name = "policy_name"
        type = ["null", "string"]
        default = null
      },
      {
        name = "violation_details"
        type = ["null", "string"]
        default = null
      },
      {
        name = "action_taken"
        type = ["null", "string"]
        default = null
      }
    ]
  })
  
  depends_on = [google_project_service.apis]
}

# Pub/Sub subscriptions for Dataflow
resource "google_pubsub_subscription" "agent_turns_dataflow" {
  name  = "agent-turns-dataflow"
  topic = google_pubsub_topic.agent_turns.name
  
  labels = local.labels
  
  # Dataflow subscription configuration
  ack_deadline_seconds       = 600  # 10 minutes for processing
  message_retention_duration = "604800s"  # 7 days
  retain_acked_messages      = false
  
  expiration_policy {
    ttl = ""  # Never expire
  }
  
  retry_policy {
    minimum_backoff = "10s"
    maximum_backoff = "600s"
  }
  
  dead_letter_policy {
    dead_letter_topic     = google_pubsub_topic.dead_letter.id
    max_delivery_attempts = 5
  }
}

resource "google_pubsub_subscription" "agent_deployments_dataflow" {
  name  = "agent-deployments-dataflow"
  topic = google_pubsub_topic.agent_deployments.name
  
  labels = local.labels
  
  ack_deadline_seconds       = 300
  message_retention_duration = "604800s"
  retain_acked_messages      = false
  
  expiration_policy {
    ttl = ""
  }
  
  retry_policy {
    minimum_backoff = "10s"
    maximum_backoff = "300s"
  }
  
  dead_letter_policy {
    dead_letter_topic     = google_pubsub_topic.dead_letter.id
    max_delivery_attempts = 5
  }
}

resource "google_pubsub_subscription" "policy_violations_dataflow" {
  name  = "policy-violations-dataflow"
  topic = google_pubsub_topic.policy_violations.name
  
  labels = local.labels
  
  ack_deadline_seconds       = 300
  message_retention_duration = "2592000s"  # 30 days
  retain_acked_messages      = false
  
  expiration_policy {
    ttl = ""
  }
  
  retry_policy {
    minimum_backoff = "10s"
    maximum_backoff = "300s"
  }
  
  dead_letter_policy {
    dead_letter_topic     = google_pubsub_topic.dead_letter.id
    max_delivery_attempts = 5
  }
}

# Dead letter topic for failed messages
resource "google_pubsub_topic" "dead_letter" {
  name = "foundry-dead-letter"
  
  labels = local.labels
  
  message_retention_duration = "2592000s"  # 30 days
  
  depends_on = [google_project_service.apis]
}

# Service account for Dataflow jobs
resource "google_service_account" "dataflow_sa" {
  account_id   = "foundry-dataflow"
  display_name = "Foundry Dataflow Service Account"
  description  = "Service account for Dataflow jobs processing agent observability data"
}

# IAM roles for Dataflow service account
resource "google_project_iam_member" "dataflow_worker" {
  project = var.project_id
  role    = "roles/dataflow.worker"
  member  = "serviceAccount:${google_service_account.dataflow_sa.email}"
}

resource "google_project_iam_member" "dataflow_bigquery" {
  project = var.project_id
  role    = "roles/bigquery.dataEditor"
  member  = "serviceAccount:${google_service_account.dataflow_sa.email}"
}

resource "google_project_iam_member" "dataflow_pubsub" {
  project = var.project_id
  role    = "roles/pubsub.subscriber"
  member  = "serviceAccount:${google_service_account.dataflow_sa.email}"
}

resource "google_project_iam_member" "dataflow_storage" {
  project = var.project_id
  role    = "roles/storage.objectAdmin"
  member  = "serviceAccount:${google_service_account.dataflow_sa.email}"
}

# Cloud Storage bucket for Dataflow staging
resource "google_storage_bucket" "dataflow_staging" {
  name          = "${var.project_id}-dataflow-staging"
  location      = var.region
  force_destroy = true
  
  labels = local.labels
  
  uniform_bucket_level_access = true
  
  lifecycle_rule {
    condition {
      age = 7  # Delete staging files after 7 days
    }
    action {
      type = "Delete"
    }
  }
  
  versioning {
    enabled = false
  }
}

# Dataflow job template for agent turns
resource "google_dataflow_flex_template_job" "agent_turns_pipeline" {
  name                    = "agent-turns-pipeline"
  container_spec_gcs_path = "gs://${google_storage_bucket.dataflow_staging.name}/templates/agent-turns-template.json"
  
  parameters = {
    inputSubscription = google_pubsub_subscription.agent_turns_dataflow.id
    outputTable       = "${var.project_id}:${google_bigquery_dataset.foundry_observability.dataset_id}.${google_bigquery_table.agent_turns.table_id}"
    tempLocation      = "gs://${google_storage_bucket.dataflow_staging.name}/temp"
  }
  
  labels = local.labels
  
  depends_on = [
    google_storage_bucket.dataflow_staging,
    google_bigquery_table.agent_turns
  ]
}

# Dataflow job template for agent deployments
resource "google_dataflow_flex_template_job" "agent_deployments_pipeline" {
  name                    = "agent-deployments-pipeline"
  container_spec_gcs_path = "gs://${google_storage_bucket.dataflow_staging.name}/templates/agent-deployments-template.json"
  
  parameters = {
    inputSubscription = google_pubsub_subscription.agent_deployments_dataflow.id
    outputTable       = "${var.project_id}:${google_bigquery_dataset.foundry_observability.dataset_id}.${google_bigquery_table.agent_deployments.table_id}"
    tempLocation      = "gs://${google_storage_bucket.dataflow_staging.name}/temp"
  }
  
  labels = local.labels
  
  depends_on = [
    google_storage_bucket.dataflow_staging,
    google_bigquery_table.agent_deployments
  ]
}

# Dataflow job template for policy violations
resource "google_dataflow_flex_template_job" "policy_violations_pipeline" {
  name                    = "policy-violations-pipeline"
  container_spec_gcs_path = "gs://${google_storage_bucket.dataflow_staging.name}/templates/policy-violations-template.json"
  
  parameters = {
    inputSubscription = google_pubsub_subscription.policy_violations_dataflow.id
    outputTable       = "${var.project_id}:${google_bigquery_dataset.foundry_observability.dataset_id}.${google_bigquery_table.policy_violations.table_id}"
    tempLocation      = "gs://${google_storage_bucket.dataflow_staging.name}/temp"
  }
  
  labels = local.labels
  
  depends_on = [
    google_storage_bucket.dataflow_staging,
    google_bigquery_table.policy_violations
  ]
}
