# Metamorphic Foundry Infrastructure Foundation
# Implements System Architecture Specification requirements:
# - VPC-only endpoints with Direct VPC Egress
# - Vertex AI Chat Endpoints and AI Applications Flow
# - BigQuery Turn Logs and Firestore Vector Store
# - Model Armor and Assured Workloads for compliance

terraform {
  required_version = ">= 1.0"
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 6.0"
    }
    google-beta = {
      source  = "hashicorp/google-beta"
      version = "~> 6.0"
    }
  }
  
  backend "gcs" {
    # Bucket and prefix will be configured via terraform init
    # Example: terraform init -backend-config="bucket=PROJECT_ID-tfstate"
  }
}

# Configure providers
provider "google" {
  project = var.project_id
  region  = var.region
  zone    = var.zone
}

provider "google-beta" {
  project = var.project_id
  region  = var.region
  zone    = var.zone
}

# Data sources
data "google_project" "project" {
  project_id = var.project_id
}

data "google_client_config" "default" {}

# Local values
locals {
  services = [
    "aiplatform.googleapis.com",           # Vertex AI
    "cloudbuild.googleapis.com",           # Cloud Build
    "run.googleapis.com",                  # Cloud Run
    "bigquery.googleapis.com",             # BigQuery
    "firestore.googleapis.com",            # Firestore
    "secretmanager.googleapis.com",        # Secret Manager
    "artifactregistry.googleapis.com",     # Artifact Registry
    "container.googleapis.com",            # GKE
    "servicenetworking.googleapis.com",    # Service Networking
    "vpcaccess.googleapis.com",            # VPC Access
    "monitoring.googleapis.com",           # Cloud Monitoring
    "logging.googleapis.com",              # Cloud Logging
    "dlp.googleapis.com",                  # Data Loss Prevention
    "cloudkms.googleapis.com",             # Cloud KMS
  ]
  
  labels = {
    project     = "metamorphic-foundry"
    environment = var.environment
    managed-by  = "terraform"
  }
}

# Enable required APIs
resource "google_project_service" "apis" {
  for_each = toset(local.services)
  
  project = var.project_id
  service = each.value
  
  disable_on_destroy = false
  
  timeouts {
    create = "30m"
    update = "40m"
  }
}

# VPC Network for Foundry
resource "google_compute_network" "foundry_vpc" {
  name                    = "foundry-vpc"
  auto_create_subnetworks = false
  mtu                     = 1460
  
  depends_on = [google_project_service.apis]
}

# Subnets for different environments
resource "google_compute_subnetwork" "foundry_staging" {
  name          = "foundry-staging-subnet"
  ip_cidr_range = "********/24"
  region        = var.region
  network       = google_compute_network.foundry_vpc.id
  
  secondary_ip_range {
    range_name    = "staging-pods"
    ip_cidr_range = "********/24"
  }
  
  secondary_ip_range {
    range_name    = "staging-services"
    ip_cidr_range = "********/24"
  }
  
  private_ip_google_access = true
}

resource "google_compute_subnetwork" "foundry_prod" {
  name          = "foundry-prod-subnet"
  ip_cidr_range = "********/24"
  region        = var.region
  network       = google_compute_network.foundry_vpc.id
  
  secondary_ip_range {
    range_name    = "prod-pods"
    ip_cidr_range = "********/24"
  }
  
  secondary_ip_range {
    range_name    = "prod-services"
    ip_cidr_range = "********/24"
  }
  
  private_ip_google_access = true
}

# VPC Access Connector for Cloud Run
resource "google_vpc_access_connector" "foundry_connector" {
  name          = "foundry-vpc-connector"
  region        = var.region
  network       = google_compute_network.foundry_vpc.name
  ip_cidr_range = "********/28"
  
  min_instances = 2
  max_instances = 10
  machine_type  = "e2-micro"
  
  depends_on = [google_project_service.apis]
}

# Global address for VPC peering (Vertex AI)
resource "google_compute_global_address" "vertex_peering" {
  name          = "vertex-ai-peering"
  purpose       = "VPC_PEERING"
  address_type  = "INTERNAL"
  prefix_length = 24
  network       = google_compute_network.foundry_vpc.id
}

# Service networking connection for Vertex AI
resource "google_service_networking_connection" "vertex_vpc_connection" {
  network                 = google_compute_network.foundry_vpc.id
  service                 = "servicenetworking.googleapis.com"
  reserved_peering_ranges = [google_compute_global_address.vertex_peering.name]
  
  depends_on = [google_project_service.apis]
}

# Firewall rules
resource "google_compute_firewall" "foundry_internal" {
  name    = "foundry-allow-internal"
  network = google_compute_network.foundry_vpc.name
  
  allow {
    protocol = "tcp"
    ports    = ["0-65535"]
  }
  
  allow {
    protocol = "udp"
    ports    = ["0-65535"]
  }
  
  allow {
    protocol = "icmp"
  }
  
  source_ranges = ["10.0.0.0/8"]
  target_tags   = ["foundry-internal"]
}

resource "google_compute_firewall" "foundry_health_checks" {
  name    = "foundry-allow-health-checks"
  network = google_compute_network.foundry_vpc.name
  
  allow {
    protocol = "tcp"
    ports    = ["8080", "8443"]
  }
  
  source_ranges = [
    "***********/22",  # Google Cloud Load Balancer health checks
    "**********/16",   # Google Cloud Load Balancer health checks
  ]
  
  target_tags = ["foundry-agent"]
}

# Cloud NAT for outbound internet access
resource "google_compute_router" "foundry_router" {
  name    = "foundry-router"
  region  = var.region
  network = google_compute_network.foundry_vpc.id
}

resource "google_compute_router_nat" "foundry_nat" {
  name                               = "foundry-nat"
  router                             = google_compute_router.foundry_router.name
  region                             = var.region
  nat_ip_allocate_option             = "AUTO_ONLY"
  source_subnetwork_ip_ranges_to_nat = "ALL_SUBNETWORKS_ALL_IP_RANGES"

  log_config {
    enable = true
    filter = "ERRORS_ONLY"
  }
}

# Artifact Registry for container images
resource "google_artifact_registry_repository" "ai_agents" {
  location      = var.region
  repository_id = "ai-agents"
  description   = "Container images for Metamorphic Foundry agents"
  format        = "DOCKER"

  labels = local.labels

  depends_on = [google_project_service.apis]
}
