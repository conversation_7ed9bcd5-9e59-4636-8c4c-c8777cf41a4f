# Monitoring and Alerting for Metamorphic Foundry
# Implements SLO-based monitoring with automated alerting

# Notification channels
resource "google_monitoring_notification_channel" "email_alerts" {
  display_name = "Foundry Email Alerts"
  type         = "email"
  
  labels = {
    email_address = var.alert_email
  }
  
  enabled = true
}

resource "google_monitoring_notification_channel" "slack_alerts" {
  display_name = "Foundry Slack Alerts"
  type         = "slack"
  
  labels = {
    channel_name = var.slack_channel
    url          = var.slack_webhook_url
  }
  
  enabled = true
}

resource "google_monitoring_notification_channel" "pagerduty_critical" {
  display_name = "Foundry PagerDuty Critical"
  type         = "pagerduty"
  
  labels = {
    service_key = var.pagerduty_service_key
  }
  
  enabled = true
}

# SLO Definitions

# Hallucination Rate SLO (≤ 2%)
resource "google_monitoring_slo" "hallucination_rate_slo" {
  service      = google_monitoring_service.foundry_agents.service_id
  display_name = "Agent Hallucination Rate SLO"
  
  goal                = 0.98  # 98% of requests should have ≤ 2% hallucination rate
  rolling_period_days = 30
  
  request_based_sli {
    good_total_ratio {
      total_service_filter = "resource.type=\"bigquery_dataset\""
      good_service_filter  = "resource.type=\"bigquery_dataset\" AND metric.label.hallucination_rate<=0.02"
    }
  }
}

# Toxicity Score SLO (≤ 0.05)
resource "google_monitoring_slo" "toxicity_score_slo" {
  service      = google_monitoring_service.foundry_agents.service_id
  display_name = "Agent Toxicity Score SLO"
  
  goal                = 0.95  # 95% of requests should have ≤ 0.05 toxicity score
  rolling_period_days = 30
  
  request_based_sli {
    good_total_ratio {
      total_service_filter = "resource.type=\"bigquery_dataset\""
      good_service_filter  = "resource.type=\"bigquery_dataset\" AND metric.label.toxicity_score<=0.05"
    }
  }
}

# Latency SLO (Model-specific)
resource "google_monitoring_slo" "flash_latency_slo" {
  service      = google_monitoring_service.foundry_agents.service_id
  display_name = "Gemini Flash Latency SLO"
  
  goal                = 0.95  # 95% of requests should be < 1s
  rolling_period_days = 7
  
  request_based_sli {
    distribution_cut {
      distribution_filter = "resource.type=\"bigquery_dataset\" AND metric.label.model=\"gemini-2.5-flash\""
      range {
        max = 1000  # 1 second in milliseconds
      }
    }
  }
}

resource "google_monitoring_slo" "pro_latency_slo" {
  service      = google_monitoring_service.foundry_agents.service_id
  display_name = "Gemini Pro Latency SLO"
  
  goal                = 0.95  # 95% of requests should be < 8s
  rolling_period_days = 7
  
  request_based_sli {
    distribution_cut {
      distribution_filter = "resource.type=\"bigquery_dataset\" AND metric.label.model=\"gemini-2.5-pro\""
      range {
        max = 8000  # 8 seconds in milliseconds
      }
    }
  }
}

# Availability SLO
resource "google_monitoring_slo" "availability_slo" {
  service      = google_monitoring_service.foundry_agents.service_id
  display_name = "Agent Availability SLO"
  
  goal                = 0.999  # 99.9% availability
  rolling_period_days = 30
  
  request_based_sli {
    good_total_ratio {
      total_service_filter = "resource.type=\"bigquery_dataset\""
      good_service_filter  = "resource.type=\"bigquery_dataset\" AND metric.label.error_message=\"\""
    }
  }
}

# Service definition
resource "google_monitoring_service" "foundry_agents" {
  service_id   = "foundry-agents"
  display_name = "Metamorphic Foundry Agents"
  
  telemetry {
    resource_name = "//bigquery.googleapis.com/projects/${var.project_id}/datasets/foundry_observability"
  }
}

# Alert Policies

# Critical: Hallucination Rate SLO Burn
resource "google_monitoring_alert_policy" "hallucination_slo_burn" {
  display_name = "Critical: Hallucination Rate SLO Burn"
  combiner     = "OR"
  enabled      = true
  
  conditions {
    display_name = "Hallucination Rate SLO Burn Rate"
    
    condition_threshold {
      filter          = "select_slo_burn_rate(\"${google_monitoring_slo.hallucination_rate_slo.name}\", \"3600s\")"
      duration        = "300s"
      comparison      = "COMPARISON_GREATER_THAN"
      threshold_value = 10.0  # 10x burn rate
      
      aggregations {
        alignment_period   = "300s"
        per_series_aligner = "ALIGN_RATE"
      }
    }
  }
  
  notification_channels = [
    google_monitoring_notification_channel.pagerduty_critical.name,
    google_monitoring_notification_channel.slack_alerts.name
  ]
  
  alert_strategy {
    auto_close = "1800s"  # 30 minutes
  }
  
  documentation {
    content = "Hallucination rate is burning through SLO budget at 10x normal rate. This indicates a significant quality issue that requires immediate attention."
  }
}

# Critical: Toxicity Score SLO Burn
resource "google_monitoring_alert_policy" "toxicity_slo_burn" {
  display_name = "Critical: Toxicity Score SLO Burn"
  combiner     = "OR"
  enabled      = true
  
  conditions {
    display_name = "Toxicity Score SLO Burn Rate"
    
    condition_threshold {
      filter          = "select_slo_burn_rate(\"${google_monitoring_slo.toxicity_score_slo.name}\", \"3600s\")"
      duration        = "300s"
      comparison      = "COMPARISON_GREATER_THAN"
      threshold_value = 10.0
      
      aggregations {
        alignment_period   = "300s"
        per_series_aligner = "ALIGN_RATE"
      }
    }
  }
  
  notification_channels = [
    google_monitoring_notification_channel.pagerduty_critical.name,
    google_monitoring_notification_channel.slack_alerts.name
  ]
  
  alert_strategy {
    auto_close = "1800s"
  }
  
  documentation {
    content = "Toxicity scores are exceeding SLA thresholds. Check Model Armor configuration and agent instructions."
  }
}

# Warning: Latency P95 Threshold
resource "google_monitoring_alert_policy" "latency_p95_warning" {
  display_name = "Warning: High P95 Latency"
  combiner     = "OR"
  enabled      = true
  
  conditions {
    display_name = "Flash Model P95 Latency"
    
    condition_threshold {
      filter          = "resource.type=\"bigquery_dataset\" AND metric.label.model=\"gemini-2.5-flash\""
      duration        = "600s"
      comparison      = "COMPARISON_GREATER_THAN"
      threshold_value = 800  # 800ms warning threshold
      
      aggregations {
        alignment_period     = "300s"
        per_series_aligner   = "ALIGN_PERCENTILE_95"
        cross_series_reducer = "REDUCE_MEAN"
      }
    }
  }
  
  conditions {
    display_name = "Pro Model P95 Latency"
    
    condition_threshold {
      filter          = "resource.type=\"bigquery_dataset\" AND metric.label.model=\"gemini-2.5-pro\""
      duration        = "600s"
      comparison      = "COMPARISON_GREATER_THAN"
      threshold_value = 6000  # 6s warning threshold
      
      aggregations {
        alignment_period     = "300s"
        per_series_aligner   = "ALIGN_PERCENTILE_95"
        cross_series_reducer = "REDUCE_MEAN"
      }
    }
  }
  
  notification_channels = [
    google_monitoring_notification_channel.slack_alerts.name,
    google_monitoring_notification_channel.email_alerts.name
  ]
  
  alert_strategy {
    auto_close = "3600s"  # 1 hour
  }
  
  documentation {
    content = "P95 latency is approaching SLA thresholds. Monitor for potential performance degradation."
  }
}

# Critical: Error Rate Spike
resource "google_monitoring_alert_policy" "error_rate_spike" {
  display_name = "Critical: Agent Error Rate Spike"
  combiner     = "OR"
  enabled      = true
  
  conditions {
    display_name = "High Error Rate"
    
    condition_threshold {
      filter          = "resource.type=\"bigquery_dataset\""
      duration        = "300s"
      comparison      = "COMPARISON_GREATER_THAN"
      threshold_value = 0.05  # 5% error rate
      
      aggregations {
        alignment_period     = "300s"
        per_series_aligner   = "ALIGN_RATE"
        cross_series_reducer = "REDUCE_MEAN"
        group_by_fields      = ["metric.label.agent_name"]
      }
    }
  }
  
  notification_channels = [
    google_monitoring_notification_channel.pagerduty_critical.name,
    google_monitoring_notification_channel.slack_alerts.name
  ]
  
  alert_strategy {
    auto_close = "1800s"
  }
  
  documentation {
    content = "Agent error rate has spiked above 5%. Check agent health and infrastructure status."
  }
}

# Warning: Model Armor Block Rate
resource "google_monitoring_alert_policy" "model_armor_blocks" {
  display_name = "Warning: High Model Armor Block Rate"
  combiner     = "OR"
  enabled      = true
  
  conditions {
    display_name = "Model Armor Block Rate"
    
    condition_threshold {
      filter          = "resource.type=\"bigquery_dataset\" AND metric.label.model_armor_verdict=\"BLOCK\""
      duration        = "900s"  # 15 minutes
      comparison      = "COMPARISON_GREATER_THAN"
      threshold_value = 0.02   # 2% block rate
      
      aggregations {
        alignment_period     = "300s"
        per_series_aligner   = "ALIGN_RATE"
        cross_series_reducer = "REDUCE_MEAN"
      }
    }
  }
  
  notification_channels = [
    google_monitoring_notification_channel.slack_alerts.name
  ]
  
  alert_strategy {
    auto_close = "3600s"
  }
  
  documentation {
    content = "Model Armor is blocking an unusually high percentage of requests. Review content patterns and policy configuration."
  }
}

# Info: Deployment Success Rate
resource "google_monitoring_alert_policy" "deployment_failures" {
  display_name = "Warning: Deployment Failures"
  combiner     = "OR"
  enabled      = true
  
  conditions {
    display_name = "Deployment Failure Rate"
    
    condition_threshold {
      filter          = "resource.type=\"bigquery_dataset\" AND metric.label.deployment_status=\"failed\""
      duration        = "600s"
      comparison      = "COMPARISON_GREATER_THAN"
      threshold_value = 0.1  # 10% failure rate
      
      aggregations {
        alignment_period     = "300s"
        per_series_aligner   = "ALIGN_RATE"
        cross_series_reducer = "REDUCE_MEAN"
      }
    }
  }
  
  notification_channels = [
    google_monitoring_notification_channel.slack_alerts.name
  ]
  
  alert_strategy {
    auto_close = "3600s"
  }
  
  documentation {
    content = "Deployment failure rate is elevated. Check CI/CD pipeline and infrastructure health."
  }
}

# Cost Monitoring
resource "google_monitoring_alert_policy" "cost_anomaly" {
  display_name = "Warning: Cost Anomaly Detected"
  combiner     = "OR"
  enabled      = true
  
  conditions {
    display_name = "Daily Cost Increase"
    
    condition_threshold {
      filter          = "resource.type=\"bigquery_dataset\""
      duration        = "3600s"
      comparison      = "COMPARISON_GREATER_THAN"
      threshold_value = 100.0  # $100 daily increase
      
      aggregations {
        alignment_period     = "86400s"  # 24 hours
        per_series_aligner   = "ALIGN_SUM"
        cross_series_reducer = "REDUCE_SUM"
      }
    }
  }
  
  notification_channels = [
    google_monitoring_notification_channel.email_alerts.name,
    google_monitoring_notification_channel.slack_alerts.name
  ]
  
  alert_strategy {
    auto_close = "86400s"  # 24 hours
  }
  
  documentation {
    content = "Daily costs have increased significantly. Review usage patterns and model efficiency."
  }
}

# Custom Metrics for Foundry-specific monitoring
resource "google_logging_metric" "agent_turn_latency" {
  name   = "foundry/agent_turn_latency"
  filter = "resource.type=\"bigquery_dataset\" AND jsonPayload.processing_time_ms>0"
  
  metric_descriptor {
    metric_kind = "GAUGE"
    value_type  = "DOUBLE"
    unit        = "ms"
    
    labels {
      key         = "agent_name"
      value_type  = "STRING"
      description = "Name of the agent"
    }
    
    labels {
      key         = "model"
      value_type  = "STRING"
      description = "Model used for processing"
    }
    
    labels {
      key         = "environment"
      value_type  = "STRING"
      description = "Environment (dev/staging/prod)"
    }
  }
  
  value_extractor = "EXTRACT(jsonPayload.processing_time_ms)"
  
  label_extractors = {
    "agent_name"  = "EXTRACT(jsonPayload.agent_name)"
    "model"       = "EXTRACT(jsonPayload.model)"
    "environment" = "EXTRACT(jsonPayload.environment)"
  }
}

resource "google_logging_metric" "hallucination_rate" {
  name   = "foundry/hallucination_rate"
  filter = "resource.type=\"bigquery_dataset\" AND jsonPayload.hallucination_rate>=0"
  
  metric_descriptor {
    metric_kind = "GAUGE"
    value_type  = "DOUBLE"
    unit        = "1"
    
    labels {
      key         = "agent_name"
      value_type  = "STRING"
      description = "Name of the agent"
    }
  }
  
  value_extractor = "EXTRACT(jsonPayload.hallucination_rate)"
  
  label_extractors = {
    "agent_name" = "EXTRACT(jsonPayload.agent_name)"
  }
}

resource "google_logging_metric" "toxicity_score" {
  name   = "foundry/toxicity_score"
  filter = "resource.type=\"bigquery_dataset\" AND jsonPayload.toxicity_score>=0"
  
  metric_descriptor {
    metric_kind = "GAUGE"
    value_type  = "DOUBLE"
    unit        = "1"
    
    labels {
      key         = "agent_name"
      value_type  = "STRING"
      description = "Name of the agent"
    }
  }
  
  value_extractor = "EXTRACT(jsonPayload.toxicity_score)"
  
  label_extractors = {
    "agent_name" = "EXTRACT(jsonPayload.agent_name)"
  }
}
