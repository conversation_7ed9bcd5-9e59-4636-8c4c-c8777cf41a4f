# Outputs for Metamorphic Foundry Infrastructure

# Network outputs
output "vpc_network_id" {
  description = "ID of the Foundry VPC network"
  value       = google_compute_network.foundry_vpc.id
}

output "vpc_network_name" {
  description = "Name of the Foundry VPC network"
  value       = google_compute_network.foundry_vpc.name
}

output "staging_subnet_id" {
  description = "ID of the staging subnet"
  value       = google_compute_subnetwork.foundry_staging.id
}

output "prod_subnet_id" {
  description = "ID of the production subnet"
  value       = google_compute_subnetwork.foundry_prod.id
}

output "vpc_connector_id" {
  description = "ID of the VPC Access Connector"
  value       = google_vpc_access_connector.foundry_connector.id
}

output "vpc_connector_name" {
  description = "Name of the VPC Access Connector"
  value       = google_vpc_access_connector.foundry_connector.name
}

# Artifact Registry outputs
output "artifact_registry_repository" {
  description = "Artifact Registry repository for agent images"
  value       = google_artifact_registry_repository.ai_agents.name
}

output "artifact_registry_url" {
  description = "URL for pushing images to Artifact Registry"
  value       = "${var.region}-docker.pkg.dev/${var.project_id}/${google_artifact_registry_repository.ai_agents.repository_id}"
}

# BigQuery outputs
output "bigquery_dataset_id" {
  description = "BigQuery dataset ID for observability"
  value       = google_bigquery_dataset.foundry_observability.dataset_id
}

output "agent_turns_table_id" {
  description = "BigQuery table ID for agent turns"
  value       = google_bigquery_table.agent_turns.table_id
}

output "agent_deployments_table_id" {
  description = "BigQuery table ID for agent deployments"
  value       = google_bigquery_table.agent_deployments.table_id
}

output "policy_violations_table_id" {
  description = "BigQuery table ID for policy violations"
  value       = google_bigquery_table.policy_violations.table_id
}

# Vertex AI outputs
output "gemini_pro_endpoint_id" {
  description = "Vertex AI endpoint ID for Gemini 2.5 Pro"
  value       = google_vertex_ai_endpoint.gemini_pro_endpoint.id
}

output "gemini_flash_endpoint_id" {
  description = "Vertex AI endpoint ID for Gemini 2.5 Flash"
  value       = google_vertex_ai_endpoint.gemini_flash_endpoint.id
}

output "feature_online_store_id" {
  description = "Vertex AI Feature Online Store ID"
  value       = google_vertex_ai_feature_online_store.foundry_features.id
}

output "feature_group_id" {
  description = "Vertex AI Feature Group ID for agent memory"
  value       = google_vertex_ai_feature_group.agent_memory.id
}

# Firestore outputs
output "firestore_database_name" {
  description = "Firestore database name for vector storage"
  value       = google_firestore_database.foundry_vectors.name
}

# Service account outputs
output "vertex_ai_service_account" {
  description = "Vertex AI service account email"
  value       = "service-${data.google_project.project.number}@gcp-sa-aiplatform.iam.gserviceaccount.com"
}

# Project information
output "project_id" {
  description = "Google Cloud Project ID"
  value       = var.project_id
}

output "project_number" {
  description = "Google Cloud Project Number"
  value       = data.google_project.project.number
}

output "region" {
  description = "Google Cloud region"
  value       = var.region
}

# URLs for external access
output "bigquery_console_url" {
  description = "URL to BigQuery console for the observability dataset"
  value       = "https://console.cloud.google.com/bigquery?project=${var.project_id}&ws=!1m4!1m3!3m2!1s${var.project_id}!2s${google_bigquery_dataset.foundry_observability.dataset_id}"
}

output "vertex_ai_console_url" {
  description = "URL to Vertex AI console"
  value       = "https://console.cloud.google.com/vertex-ai?project=${var.project_id}"
}

output "artifact_registry_console_url" {
  description = "URL to Artifact Registry console"
  value       = "https://console.cloud.google.com/artifacts/docker/${var.project_id}/${var.region}/${google_artifact_registry_repository.ai_agents.repository_id}?project=${var.project_id}"
}

# Configuration for applications
output "foundry_config" {
  description = "Configuration object for Foundry applications"
  value = {
    project_id                = var.project_id
    region                   = var.region
    vpc_network              = google_compute_network.foundry_vpc.name
    vpc_connector            = google_vpc_access_connector.foundry_connector.name
    artifact_registry_url    = "${var.region}-docker.pkg.dev/${var.project_id}/${google_artifact_registry_repository.ai_agents.repository_id}"
    bigquery_dataset         = google_bigquery_dataset.foundry_observability.dataset_id
    firestore_database       = google_firestore_database.foundry_vectors.name
    gemini_pro_endpoint      = google_vertex_ai_endpoint.gemini_pro_endpoint.name
    gemini_flash_endpoint    = google_vertex_ai_endpoint.gemini_flash_endpoint.name
    feature_online_store     = google_vertex_ai_feature_online_store.foundry_features.name
    staging_subnet           = google_compute_subnetwork.foundry_staging.name
    prod_subnet              = google_compute_subnetwork.foundry_prod.name
  }
  sensitive = false
}

# Terraform state information
output "terraform_state_bucket" {
  description = "GCS bucket for Terraform state (to be configured)"
  value       = "${var.project_id}-tfstate"
}

# Resource counts for monitoring
output "resource_summary" {
  description = "Summary of created resources"
  value = {
    vpc_networks              = 1
    subnets                  = 2
    firewall_rules           = 2
    bigquery_datasets        = 1
    bigquery_tables          = 5
    vertex_ai_endpoints      = 2
    feature_stores           = 1
    feature_groups           = 1
    firestore_databases      = 1
    artifact_registries      = 1
    vpc_connectors           = 1
    nat_gateways            = 1
  }
}
