# Variables for Metamorphic Foundry Infrastructure

variable "project_id" {
  description = "Google Cloud Project ID"
  type        = string
  validation {
    condition     = can(regex("^[a-z][a-z0-9-]{4,28}[a-z0-9]$", var.project_id))
    error_message = "Project ID must be 6-30 characters, start with a letter, and contain only lowercase letters, numbers, and hyphens."
  }
}

variable "region" {
  description = "Google Cloud region for resources"
  type        = string
  default     = "us-central1"
  validation {
    condition = contains([
      "us-central1", "us-east1", "us-east4", "us-west1", "us-west2", "us-west3", "us-west4",
      "europe-west1", "europe-west2", "europe-west3", "europe-west4", "europe-west6",
      "asia-east1", "asia-northeast1", "asia-southeast1", "australia-southeast1"
    ], var.region)
    error_message = "Region must be a valid Google Cloud region."
  }
}

variable "zone" {
  description = "Google Cloud zone for zonal resources"
  type        = string
  default     = "us-central1-a"
}

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
  default     = "dev"
  validation {
    condition     = contains(["dev", "staging", "prod"], var.environment)
    error_message = "Environment must be one of: dev, staging, prod."
  }
}

variable "organization_domain" {
  description = "Organization domain for IAM access"
  type        = string
  default     = ""
}

variable "enable_apis" {
  description = "Whether to enable required Google Cloud APIs"
  type        = bool
  default     = true
}

variable "vpc_cidr_ranges" {
  description = "CIDR ranges for VPC subnets"
  type = object({
    staging_subnet = string
    prod_subnet    = string
    vpc_connector  = string
    vertex_peering = number
  })
  default = {
    staging_subnet = "********/24"
    prod_subnet    = "********/24"
    vpc_connector  = "********/28"
    vertex_peering = 24
  }
}

variable "bigquery_location" {
  description = "BigQuery dataset location"
  type        = string
  default     = "US"
  validation {
    condition = contains([
      "US", "EU", "asia-east1", "asia-northeast1", "asia-southeast1",
      "australia-southeast1", "europe-west1", "europe-west2", "us-central1", "us-east1"
    ], var.bigquery_location)
    error_message = "BigQuery location must be a valid location."
  }
}

variable "retention_days" {
  description = "Data retention period in days"
  type        = number
  default     = 400
  validation {
    condition     = var.retention_days >= 1 && var.retention_days <= 3650
    error_message = "Retention days must be between 1 and 3650 (10 years)."
  }
}

variable "enable_private_google_access" {
  description = "Enable Private Google Access for subnets"
  type        = bool
  default     = true
}

variable "enable_flow_logs" {
  description = "Enable VPC Flow Logs"
  type        = bool
  default     = true
}

variable "vertex_ai_config" {
  description = "Vertex AI configuration"
  type = object({
    enable_request_response_logging = bool
    pro_sampling_rate              = number
    flash_sampling_rate            = number
    enable_private_endpoints       = bool
  })
  default = {
    enable_request_response_logging = true
    pro_sampling_rate              = 1.0
    flash_sampling_rate            = 0.1
    enable_private_endpoints       = true
  }
  validation {
    condition = (
      var.vertex_ai_config.pro_sampling_rate >= 0.0 &&
      var.vertex_ai_config.pro_sampling_rate <= 1.0 &&
      var.vertex_ai_config.flash_sampling_rate >= 0.0 &&
      var.vertex_ai_config.flash_sampling_rate <= 1.0
    )
    error_message = "Sampling rates must be between 0.0 and 1.0."
  }
}

variable "feature_store_config" {
  description = "Feature store configuration"
  type = object({
    min_node_count         = number
    max_node_count         = number
    cpu_utilization_target = number
  })
  default = {
    min_node_count         = 1
    max_node_count         = 10
    cpu_utilization_target = 70
  }
  validation {
    condition = (
      var.feature_store_config.min_node_count >= 1 &&
      var.feature_store_config.max_node_count >= var.feature_store_config.min_node_count &&
      var.feature_store_config.cpu_utilization_target >= 10 &&
      var.feature_store_config.cpu_utilization_target <= 90
    )
    error_message = "Invalid feature store configuration values."
  }
}

variable "vpc_connector_config" {
  description = "VPC Access Connector configuration"
  type = object({
    min_instances = number
    max_instances = number
    machine_type  = string
  })
  default = {
    min_instances = 2
    max_instances = 10
    machine_type  = "e2-micro"
  }
  validation {
    condition = contains(["e2-micro", "e2-standard-4", "f1-micro"], var.vpc_connector_config.machine_type)
    error_message = "Machine type must be one of: e2-micro, e2-standard-4, f1-micro."
  }
}

variable "firewall_source_ranges" {
  description = "Source IP ranges for firewall rules"
  type = object({
    internal_ranges     = list(string)
    health_check_ranges = list(string)
  })
  default = {
    internal_ranges = ["10.0.0.0/8"]
    health_check_ranges = [
      "***********/22",  # Google Cloud Load Balancer health checks
      "**********/16"    # Google Cloud Load Balancer health checks
    ]
  }
}

variable "labels" {
  description = "Additional labels to apply to resources"
  type        = map(string)
  default     = {}
  validation {
    condition = alltrue([
      for k, v in var.labels : can(regex("^[a-z][a-z0-9_-]{0,62}$", k))
    ])
    error_message = "Label keys must start with a letter and contain only lowercase letters, numbers, underscores, and hyphens."
  }
}

variable "enable_deletion_protection" {
  description = "Enable deletion protection for critical resources"
  type        = bool
  default     = true
}

variable "backup_retention_days" {
  description = "Backup retention period in days"
  type        = number
  default     = 30
  validation {
    condition     = var.backup_retention_days >= 1 && var.backup_retention_days <= 365
    error_message = "Backup retention days must be between 1 and 365."
  }
}

variable "monitoring_config" {
  description = "Monitoring and alerting configuration"
  type = object({
    enable_uptime_checks    = bool
    notification_channels   = list(string)
    alert_policy_enabled   = bool
    log_retention_days     = number
  })
  default = {
    enable_uptime_checks  = true
    notification_channels = []
    alert_policy_enabled  = true
    log_retention_days    = 30
  }
}
