# Vertex AI resources for Metamorphic Foundry
# Implements System Architecture §4.4: Runtime Tiering with Vertex AI endpoints

# Vertex AI Endpoint for Gemini 2.5 Pro (heavy reasoning)
resource "google_vertex_ai_endpoint" "gemini_pro_endpoint" {
  name         = "foundry-gemini-pro"
  display_name = "Foundry Gemini 2.5 Pro Endpoint"
  description  = "Endpoint for whole-doc reasoning and code refactors"
  location     = var.region
  region       = var.region
  
  labels = merge(local.labels, {
    model = "gemini-2.5-pro"
    tier  = "heavy-reasoning"
  })
  
  # VPC-only endpoint for security
  network = "projects/${data.google_project.project.number}/global/networks/${google_compute_network.foundry_vpc.name}"
  
  # Request/response logging to BigQuery
  predict_request_response_logging_config {
    bigquery_destination {
      output_uri = "bq://${data.google_project.project.project_id}.${google_bigquery_dataset.foundry_observability.dataset_id}.vertex_ai_logs"
    }
    enabled       = true
    sampling_rate = 1.0  # Log all requests for compliance
  }
  
  depends_on = [
    google_service_networking_connection.vertex_vpc_connection,
    google_bigquery_dataset.foundry_observability
  ]
}

# Vertex AI Endpoint for Gemini 2.5 Flash (fast responses)
resource "google_vertex_ai_endpoint" "gemini_flash_endpoint" {
  name         = "foundry-gemini-flash"
  display_name = "Foundry Gemini 2.5 Flash Endpoint"
  description  = "Endpoint for <1s chat, routing, and quick classification"
  location     = var.region
  region       = var.region
  
  labels = merge(local.labels, {
    model = "gemini-2.5-flash"
    tier  = "fast-response"
  })
  
  network = "projects/${data.google_project.project.number}/global/networks/${google_compute_network.foundry_vpc.name}"
  
  predict_request_response_logging_config {
    bigquery_destination {
      output_uri = "bq://${data.google_project.project.project_id}.${google_bigquery_dataset.foundry_observability.dataset_id}.vertex_ai_logs"
    }
    enabled       = true
    sampling_rate = 0.1  # Sample 10% for performance
  }
  
  depends_on = [
    google_service_networking_connection.vertex_vpc_connection,
    google_bigquery_dataset.foundry_observability
  ]
}

# BigQuery table for Vertex AI request/response logs
resource "google_bigquery_table" "vertex_ai_logs" {
  dataset_id          = google_bigquery_dataset.foundry_observability.dataset_id
  table_id            = "vertex_ai_logs"
  deletion_protection = false
  
  description = "Vertex AI endpoint request/response logs"
  
  labels = local.labels
  
  time_partitioning {
    type  = "DAY"
    field = "timestamp"
  }
  
  schema = jsonencode([
    {
      name = "timestamp"
      type = "TIMESTAMP"
      mode = "REQUIRED"
      description = "Request timestamp"
    },
    {
      name = "endpoint_id"
      type = "STRING"
      mode = "REQUIRED"
      description = "Vertex AI endpoint identifier"
    },
    {
      name = "model_name"
      type = "STRING"
      mode = "REQUIRED"
      description = "Model name used"
    },
    {
      name = "request_id"
      type = "STRING"
      mode = "REQUIRED"
      description = "Unique request identifier"
    },
    {
      name = "input_token_count"
      type = "INTEGER"
      mode = "NULLABLE"
      description = "Number of input tokens"
    },
    {
      name = "output_token_count"
      type = "INTEGER"
      mode = "NULLABLE"
      description = "Number of output tokens"
    },
    {
      name = "latency_ms"
      type = "FLOAT"
      mode = "NULLABLE"
      description = "Request latency in milliseconds"
    },
    {
      name = "status_code"
      type = "INTEGER"
      mode = "REQUIRED"
      description = "HTTP status code"
    },
    {
      name = "error_message"
      type = "STRING"
      mode = "NULLABLE"
      description = "Error message if request failed"
    }
  ])
}

# Firestore database for vector storage
resource "google_firestore_database" "foundry_vectors" {
  project                           = var.project_id
  name                             = "foundry-vectors"
  location_id                      = var.region
  type                             = "FIRESTORE_NATIVE"
  concurrency_mode                 = "OPTIMISTIC"
  app_engine_integration_mode      = "DISABLED"
  point_in_time_recovery_enablement = "POINT_IN_TIME_RECOVERY_ENABLED"
  delete_protection_state          = "DELETE_PROTECTION_DISABLED"
  
  depends_on = [google_project_service.apis]
}

# Feature Online Store for real-time feature serving
resource "google_vertex_ai_feature_online_store" "foundry_features" {
  name   = "foundry-feature-store"
  region = var.region
  
  labels = merge(local.labels, {
    component = "feature-store"
  })
  
  # Bigtable backend with auto-scaling
  bigtable {
    auto_scaling {
      min_node_count         = 1
      max_node_count         = 10
      cpu_utilization_target = 70
    }
  }
  
  depends_on = [google_project_service.apis]
}

# Feature Group for agent memory patterns
resource "google_vertex_ai_feature_group" "agent_memory" {
  name        = "agent-memory-features"
  description = "Feature group for agent conversation memory and context"
  region      = var.region
  
  labels = merge(local.labels, {
    component = "agent-memory"
  })
  
  big_query {
    big_query_source {
      input_uri = "bq://${google_bigquery_table.agent_memory_features.project}.${google_bigquery_table.agent_memory_features.dataset_id}.${google_bigquery_table.agent_memory_features.table_id}"
    }
    entity_id_columns = ["agent_id", "conversation_id"]
  }
  
  depends_on = [google_bigquery_table.agent_memory_features]
}

# BigQuery table for agent memory features
resource "google_bigquery_table" "agent_memory_features" {
  dataset_id          = google_bigquery_dataset.foundry_observability.dataset_id
  table_id            = "agent_memory_features"
  deletion_protection = false
  
  description = "Agent memory features for context and personalization"
  
  labels = local.labels
  
  time_partitioning {
    type  = "DAY"
    field = "feature_timestamp"
  }
  
  schema = jsonencode([
    {
      name = "agent_id"
      type = "STRING"
      mode = "REQUIRED"
      description = "Agent instance identifier"
    },
    {
      name = "conversation_id"
      type = "STRING"
      mode = "REQUIRED"
      description = "Conversation identifier"
    },
    {
      name = "feature_timestamp"
      type = "TIMESTAMP"
      mode = "REQUIRED"
      description = "Feature timestamp for temporal features"
    },
    {
      name = "user_preferences"
      type = "JSON"
      mode = "NULLABLE"
      description = "User preference features"
    },
    {
      name = "conversation_context"
      type = "JSON"
      mode = "NULLABLE"
      description = "Conversation context features"
    },
    {
      name = "topic_embeddings"
      type = "FLOAT"
      mode = "REPEATED"
      description = "Topic embedding vectors"
    },
    {
      name = "sentiment_score"
      type = "FLOAT"
      mode = "NULLABLE"
      description = "Conversation sentiment score"
    },
    {
      name = "complexity_score"
      type = "FLOAT"
      mode = "NULLABLE"
      description = "Query complexity score"
    }
  ])
}

# IAM for Vertex AI service account to access BigQuery
resource "google_bigquery_dataset_iam_member" "vertex_ai_access" {
  dataset_id = google_bigquery_dataset.foundry_observability.dataset_id
  role       = "roles/bigquery.dataViewer"
  member     = "serviceAccount:service-${data.google_project.project.number}@gcp-sa-aiplatform.iam.gserviceaccount.com"
  
  depends_on = [google_vertex_ai_feature_online_store.foundry_features]
}
